# Web Translator Chrome插件

这是一个谷歌浏览器扩展，可以将网页内容发送到后端进行翻译，然后显示翻译后的内容。

## 功能

- 一键翻译当前网页内容
- 支持多种目标语言
- 可配置的翻译API接口
- 可自定义API密钥
- 原文/译文一键切换

## 安装方法

### 开发模式安装

1. 下载或克隆此仓库到本地
2. 打开Chrome浏览器，进入扩展管理页面 (chrome://extensions/)
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择此项目文件夹

## 使用方法

1. 安装插件后，在任意网页点击浏览器工具栏中的插件图标
2. 在弹出窗口中点击"翻译此页面"按钮
3. 等待翻译完成，页面内容将被替换为翻译后的内容
4. 再次点击可以恢复原始内容

## 配置选项

在插件弹出窗口中可以配置以下选项：

- 目标语言：选择需要翻译成的语言
- 自动翻译：启用后将自动翻译打开的页面
- API端点：配置自定义的翻译API地址
- API密钥：如果API需要密钥认证，可在此处设置

## 连接自定义后端

默认情况下，插件使用示例API地址。要连接到实际的翻译服务：

1. 在插件设置中输入有效的API端点URL
2. 如需要，添加API密钥
3. 确保您的API接受以下JSON格式请求：

```json
{
  "text": "要翻译的文本",
  "source": "源语言代码",
  "target": "目标语言代码"
}
```

并返回以下格式的响应：

```json
{
  "translatedText": "翻译后的文本"
}
```

## 注意事项

- 此插件默认包含一个模拟翻译功能，仅用于测试
- 实际使用时请连接到真实的翻译API
- 请确保遵循您使用的翻译API的使用条款和限制

## 隐私说明

此扩展会将网页内容发送到配置的翻译API。请确保您使用的API遵循您所在地区的隐私法规。 