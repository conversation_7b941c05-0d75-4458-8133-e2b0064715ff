// 弹出界面脚本
document.addEventListener('DOMContentLoaded', () => {
  // 在页面中添加状态显示区域
  const statusArea = document.createElement('div');
  statusArea.id = 'debug-status-area';
  statusArea.style.marginTop = '10px';
  statusArea.style.padding = '10px';
  statusArea.style.border = '1px solid #ddd';
  statusArea.style.borderRadius = '4px';
  statusArea.style.fontSize = '12px';
  statusArea.style.fontFamily = 'monospace';
  statusArea.style.maxHeight = '150px';
  statusArea.style.overflow = 'auto';
  
  // 添加标题
  const statusTitle = document.createElement('div');
  statusTitle.textContent = '扩展状态日志';
  statusTitle.style.fontWeight = 'bold';
  statusTitle.style.marginBottom = '5px';
  statusArea.appendChild(statusTitle);
  
  // 添加日志内容容器
  const statusContent = document.createElement('div');
  statusContent.id = 'status-log-content';
  statusArea.appendChild(statusContent);
  
  // 添加到页面
  document.body.appendChild(statusArea);
  
  // 添加日志函数
  window.addStatusLog = function(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    logEntry.style.borderBottom = '1px solid #eee';
    logEntry.style.paddingBottom = '3px';
    logEntry.style.marginBottom = '3px';
    
    // 根据类型设置颜色
    switch(type) {
      case 'error':
        logEntry.style.color = '#e53935';
        break;
      case 'success':
        logEntry.style.color = '#43a047';
        break;
      case 'warning':
        logEntry.style.color = '#fb8c00';
        break;
      default:
        logEntry.style.color = '#2196f3';
    }
    
    statusContent.appendChild(logEntry);
    statusArea.scrollTop = statusArea.scrollHeight;
  };
  
  // 记录初始状态
  window.addStatusLog('扩展已加载', 'info');
  
  const translateButton = document.getElementById('translateButton');
  const statusElement = document.getElementById('status');
  const targetLanguageSelect = document.getElementById('targetLanguage');
  const autoTranslateCheckbox = document.getElementById('autoTranslate');
  const apiEndpointInput = document.getElementById('apiEndpoint');
  const apiKeyInput = document.getElementById('apiKey');
  const aiServiceSelect = document.getElementById('aiService');
  const promptTemplateInput = document.getElementById('promptTemplate');
  const bypassModelValidationCheckbox = document.getElementById('bypassModelValidation');
  
  // 新增的配置元素
  const maxChunkSizeInput = document.getElementById('maxChunkSize');
  const concurrentRequestsInput = document.getElementById('concurrentRequests');
  const translationPercentageInput = document.getElementById('translationPercentage');
  const percentageValueDisplay = document.getElementById('percentageValue');
  const translationModeSelect = document.getElementById('translationMode');
  
  // 新增的翻译样式设置
  const translationStyleSelect = document.getElementById('translationStyle');
  const underlineColorInput = document.getElementById('underlineColor');
  
  // AI模型选择器
  const openaiModelSelect = document.getElementById('openaiModel');
  const deepseekModelSelect = document.getElementById('deepseekModel');
  const qwenModelSelect = document.getElementById('qwenModel');
  const customModelInput = document.getElementById('customModel');
  
  // 标签页
  const tabs = document.querySelectorAll('.tab');
  const tabContents = document.querySelectorAll('.tab-content');
  
  let isTranslated = false;
  let config = {
    apiEndpoint: 'https://api.example.com/translate',
    defaultTargetLanguage: 'zh-CN',
    enableAutoTranslate: false,
    apiKey: '',
    aiService: 'openai',
    aiModel: 'gpt-3.5-turbo',
    promptTemplate: "请将下面的文本从{sourceLanguage}翻译成{targetLanguage}，保持原文的格式和语气：\n\n{text}",
    bypassModelValidation: false,
    // 新增配置项
    maxChunkSize: 2000,
    concurrentRequests: 2,
    translationPercentage: 100,
    translationMode: 'random',
    // 翻译样式配置
    translationStyle: 'replace',
    underlineColor: '#4285f4'
  };
  
  // 标签页切换逻辑
  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // 移除所有激活状态
      tabs.forEach(t => t.classList.remove('active'));
      tabContents.forEach(c => c.classList.remove('active'));
      
      // 添加当前激活状态
      tab.classList.add('active');
      const tabId = tab.getAttribute('data-tab');
      document.getElementById(`${tabId}-tab`).classList.add('active');
    });
  });
  
  // AI服务切换逻辑
  aiServiceSelect.addEventListener('change', function() {
    const service = this.value;
    
    // 更新模型选项显示
    updateModelOptionsVisibility(service);
    
    // 隐藏模型警告
    document.getElementById('model-warning').style.display = 'none';
    
    // 自动切换到高级设置标签，让用户选择相应的模型
    switchToAdvancedTab();
    
    // 添加提示
    window.addStatusLog(`已切换到${getServiceName(service)}服务，请在高级设置中选择相应模型`, 'info');
    
    // 立即保存当前服务设置
    saveSettings();
  });
  
  // 切换到高级设置标签
  function switchToAdvancedTab() {
    // 移除所有激活状态
    document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
    
    // 添加当前激活状态
    document.querySelector('.tab[data-tab="advanced"]').classList.add('active');
    document.getElementById('advanced-tab').classList.add('active');
  }
  
  // 获取服务的中文名称
  function getServiceName(service) {
    switch(service) {
      case 'openai':
        return 'OpenAI';
      case 'deepseek':
        return 'Deepseek';
      case 'qwen':
        return '通义千问';
      case 'custom':
        return '自定义API';
      case 'mock':
        return '模拟翻译';
      default:
        return service;
    }
  }
  
  // 获取当前页面的状态和配置
  initializePopup();
  
  // 添加调试日志函数
  function debugLog(message, data = null) {
    console.log(`[Translator Popup Debug] ${message}`, data || '');
  }
  
  // 添加模型错误检测
  function checkForModelErrors(responseText) {
    const modelWarning = document.getElementById('model-warning');
    
    // 检查是否包含特定的错误消息
    if (responseText && responseText.includes('Model Not Exist')) {
      modelWarning.style.display = 'block';
      modelWarning.textContent = '警告：您选择的模型不存在或API密钥无权访问该模型。请检查模型名称是否正确，或参考API提供商的文档。';
      return true;
    }
    
    // 检查其他常见错误
    if (responseText && (
      responseText.includes('Invalid API key') || 
      responseText.includes('Authentication failed') ||
      responseText.includes('Unauthorized')
    )) {
      modelWarning.style.display = 'block';
      modelWarning.textContent = '警告：API密钥无效或认证失败。请检查您的API密钥是否正确。';
      return true;
    }
    
    modelWarning.style.display = 'none';
    return false;
  }
  
  // 翻译按钮点击事件
  translateButton.addEventListener('click', async () => {
    debugLog('翻译按钮被点击');
    
    // 记录到状态区域
    window.addStatusLog('翻译按钮被点击', 'info');
    
    // 禁用按钮防止重复点击
    translateButton.disabled = true;
    translateButton.textContent = '处理中...';
    
    try {
      // 获取当前活动标签页
      const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!activeTab) {
        window.addStatusLog('未找到活动标签页', 'error');
        translateButton.disabled = false;
        translateButton.textContent = '翻译此页面';
        return;
      }
      
      window.addStatusLog(`获取到标签页 ID: ${activeTab.id}`, 'info');
      
      // 检查URL是否有效
      if (!activeTab.url || activeTab.url.startsWith('chrome:') || activeTab.url.startsWith('chrome-extension:')) {
        window.addStatusLog(`无法在此页面使用翻译功能: ${activeTab.url}`, 'error');
        translateButton.disabled = false;
        translateButton.textContent = '翻译此页面';
        return;
      }
      
      // 尝试发送ping消息验证内容脚本
      window.addStatusLog('验证内容脚本...', 'info');
      
      // 设置超时
      const pingTimeout = setTimeout(() => {
        window.addStatusLog('内容脚本响应超时', 'error');
        translateButton.disabled = false;
        translateButton.textContent = '翻译此页面';
      }, 3000);
      
      // 尝试ping
      chrome.tabs.sendMessage(activeTab.id, { action: 'ping' }, pingResponse => {
        clearTimeout(pingTimeout);
        
        if (chrome.runtime.lastError) {
          window.addStatusLog(`内容脚本未响应: ${chrome.runtime.lastError.message}`, 'error');
          window.addStatusLog('尝试注入内容脚本...', 'warning');
          
          // 尝试注入内容脚本
          chrome.scripting.executeScript({
            target: { tabId: activeTab.id },
            files: ['content.js']
          })
          .then(() => {
            window.addStatusLog('内容脚本注入成功，正在重试...', 'success');
            
            // 等待脚本加载
            setTimeout(() => {
              // 重新尝试翻译
              sendTranslateMessage(activeTab.id);
            }, 1000);
          })
          .catch(err => {
            window.addStatusLog(`注入内容脚本失败: ${err.message}`, 'error');
            translateButton.disabled = false;
            translateButton.textContent = '翻译此页面';
          });
          return;
        }
        
        window.addStatusLog('内容脚本已响应', 'success');
        // 内容脚本已加载，发送翻译请求
        sendTranslateMessage(activeTab.id);
      });
    } catch (error) {
      window.addStatusLog(`出现异常: ${error.message}`, 'error');
      translateButton.disabled = false;
      translateButton.textContent = '翻译此页面';
    }
    
    // 发送翻译消息
    function sendTranslateMessage(tabId) {
      window.addStatusLog('发送翻译请求...', 'info');
      
      // 设置超时
      const translateTimeout = setTimeout(() => {
        window.addStatusLog('翻译请求超时', 'error');
        translateButton.disabled = false;
        translateButton.textContent = '翻译此页面';
      }, 30000); // 30秒超时
      
      chrome.tabs.sendMessage(tabId, { action: 'translate' }, response => {
        clearTimeout(translateTimeout);
        
        if (chrome.runtime.lastError) {
          window.addStatusLog(`翻译请求错误: ${chrome.runtime.lastError.message}`, 'error');
          translateButton.disabled = false;
          translateButton.textContent = '翻译此页面';
          return;
        }
        
        if (response && response.success) {
          // 显示成功信息和元数据
          window.addStatusLog('翻译成功', 'success');
          
          // 如果有元数据，显示详细信息
          if (response.metadata) {
            window.addStatusLog(`服务: ${response.metadata.service}`, 'info');
            window.addStatusLog(`模型: ${response.metadata.model}`, 'info');
            window.addStatusLog(`耗时: ${response.metadata.duration}秒`, 'info');
            window.addStatusLog(`时间: ${new Date(response.metadata.timestamp).toLocaleTimeString()}`, 'info');
          }
          
          // 创建按钮容器
          const btnContainer = document.createElement('div');
          btnContainer.style.display = 'flex';
          btnContainer.style.gap = '10px';
          btnContainer.style.marginTop = '10px';
          document.getElementById('status-log-content').appendChild(btnContainer);
          
          // 如果有请求数据，显示请求详情
          if (response.requestData) {
            // 创建查看请求按钮
            const requestBtn = document.createElement('button');
            requestBtn.textContent = '查看API请求';
            requestBtn.style.padding = '5px 10px';
            requestBtn.style.backgroundColor = '#4285f4';
            requestBtn.style.color = 'white';
            requestBtn.style.border = 'none';
            requestBtn.style.borderRadius = '4px';
            requestBtn.style.cursor = 'pointer';
            requestBtn.style.flex = '1';
            
            requestBtn.onclick = () => {
              // 创建请求详情窗口
              const requestWindow = window.open('', 'API请求详情', 'width=800,height=600');
              requestWindow.document.write(`
                <!DOCTYPE html>
                <html>
                  <head>
                    <title>API请求详情</title>
                    <style>
                      body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        margin: 20px;
                        background-color: #f9f9f9;
                      }
                      h1 {
                        color: #4285f4;
                        border-bottom: 1px solid #ddd;
                        padding-bottom: 10px;
                      }
                      .info-section {
                        margin-bottom: 20px;
                      }
                      .info-section h2 {
                        color: #333;
                        font-size: 1.2em;
                        margin-bottom: 10px;
                      }
                      .endpoint {
                        font-weight: bold;
                        color: #0d47a1;
                        margin-bottom: 10px;
                      }
                      .method {
                        display: inline-block;
                        padding: 3px 8px;
                        background-color: #4285f4;
                        color: white;
                        border-radius: 4px;
                        margin-right: 10px;
                      }
                      table {
                        width: 100%;
                        border-collapse: collapse;
                        margin-bottom: 15px;
                      }
                      th, td {
                        text-align: left;
                        padding: 8px;
                        border: 1px solid #ddd;
                      }
                      th {
                        background-color: #f2f2f2;
                      }
                      pre {
                        background-color: #f5f5f5;
                        padding: 15px;
                        border-radius: 5px;
                        overflow: auto;
                        max-height: 400px;
                      }
                    </style>
                  </head>
                  <body>
                    <h1>API请求详情</h1>
                    
                    <div class="info-section">
                      <h2>请求端点</h2>
                      <div>
                        <span class="method">${response.requestData.method}</span>
                        <span class="endpoint">${response.requestData.url}</span>
                      </div>
                    </div>
                    
                    <div class="info-section">
                      <h2>请求头</h2>
                      <table>
                        <thead>
                          <tr>
                            <th>名称</th>
                            <th>值</th>
                          </tr>
                        </thead>
                        <tbody>
                          ${Object.entries(response.requestData.headers).map(([key, value]) => `
                            <tr>
                              <td>${key}</td>
                              <td>${value}</td>
                            </tr>
                          `).join('')}
                        </tbody>
                      </table>
                    </div>
                    
                    <div class="info-section">
                      <h2>请求体</h2>
                      <pre>${JSON.stringify(response.requestData.body, null, 2)}</pre>
                    </div>
                  </body>
                </html>
              `);
            };
            
            // 添加到容器
            btnContainer.appendChild(requestBtn);
          }
          
          // 如果有原始响应，显示响应详情
          if (response.originalResponse) {
            // 创建查看响应按钮
            const responseBtn = document.createElement('button');
            responseBtn.textContent = '查看API响应';
            responseBtn.style.padding = '5px 10px';
            responseBtn.style.backgroundColor = '#0f9d58';
            responseBtn.style.color = 'white';
            responseBtn.style.border = 'none';
            responseBtn.style.borderRadius = '4px';
            responseBtn.style.cursor = 'pointer';
            responseBtn.style.flex = '1';
            
            responseBtn.onclick = () => {
              // 创建响应详情窗口
              const responseWindow = window.open('', 'API响应详情', 'width=800,height=600');
              responseWindow.document.write(`
                <!DOCTYPE html>
                <html>
                  <head>
                    <title>API响应详情</title>
                    <style>
                      body {
                        font-family: Arial, sans-serif;
                        line-height: 1.6;
                        margin: 20px;
                        background-color: #f9f9f9;
                      }
                      h1 {
                        color: #0f9d58;
                        border-bottom: 1px solid #ddd;
                        padding-bottom: 10px;
                      }
                      pre {
                        background-color: #f5f5f5;
                        padding: 15px;
                        border-radius: 5px;
                        overflow: auto;
                        max-height: 400px;
                      }
                    </style>
                  </head>
                  <body>
                    <h1>API响应详情</h1>
                    <pre>${JSON.stringify(response.originalResponse, null, 2)}</pre>
                  </body>
                </html>
              `);
            };
            
            // 添加到容器
            btnContainer.appendChild(responseBtn);
          }
          
          isTranslated = response.isTranslated;
          translateButton.textContent = isTranslated ? '恢复原文' : '翻译此页面';
        } else {
          // 显示错误信息
          window.addStatusLog(`翻译失败: ${response?.error || '未知错误'}`, 'error');
          
          // 检查是否是模型错误
          if (response && response.error) {
            checkForModelErrors(response.error);
          }
          
          // 如果有详细错误信息
          if (response && response.details) {
            window.addStatusLog(`错误详情: ${response.details}`, 'error');
          }
          
          translateButton.textContent = '翻译此页面';
        }
        
        translateButton.disabled = false;
      });
    }
  });
  
  // 更新翻译样式相关UI
  translationStyleSelect.addEventListener('change', function() {
    const showColorPicker = this.value === 'underline';
    document.getElementById('underlineColor').parentElement.style.display = 
      showColorPicker ? 'flex' : 'none';
  });
  
  // 更新翻译百分比显示
  translationPercentageInput.addEventListener('input', function() {
    percentageValueDisplay.textContent = `${this.value}%`;
  });
  
  // 设置更改事件
  targetLanguageSelect.addEventListener('change', saveSettings);
  autoTranslateCheckbox.addEventListener('change', saveSettings);
  apiEndpointInput.addEventListener('blur', saveSettings);
  apiKeyInput.addEventListener('blur', saveSettings);
  aiServiceSelect.addEventListener('change', saveSettings);
  promptTemplateInput.addEventListener('blur', saveSettings);
  bypassModelValidationCheckbox.addEventListener('change', saveSettings);
  
  // 新增设置更改事件
  maxChunkSizeInput.addEventListener('change', saveSettings);
  concurrentRequestsInput.addEventListener('change', saveSettings);
  translationPercentageInput.addEventListener('change', saveSettings);
  translationModeSelect.addEventListener('change', saveSettings);
  translationStyleSelect.addEventListener('change', saveSettings);
  underlineColorInput.addEventListener('change', saveSettings);
  
  // AI模型选择事件
  openaiModelSelect.addEventListener('change', saveAIModel);
  deepseekModelSelect.addEventListener('change', saveAIModel);
  qwenModelSelect.addEventListener('change', saveAIModel);
  customModelInput.addEventListener('blur', saveAIModel);
  
  // 初始化弹出界面
  async function initializePopup() {
    // 获取后台配置
    chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
      if (response && response.success) {
        config = response.config;
        
        // 调试输出配置信息
        debugLog('加载配置', config);
        
        // 填充设置
        targetLanguageSelect.value = config.defaultTargetLanguage;
        autoTranslateCheckbox.checked = config.enableAutoTranslate;
        apiEndpointInput.value = config.apiEndpoint || '';
        apiKeyInput.value = config.apiKey || '';
        aiServiceSelect.value = config.aiService || 'openai';
        promptTemplateInput.value = config.promptTemplate || "请将下面的文本从{sourceLanguage}翻译成{targetLanguage}，保持原文的格式和语气：\n\n{text}";
        bypassModelValidationCheckbox.checked = config.bypassModelValidation || false;
        
        // 新增设置项填充
        maxChunkSizeInput.value = config.maxChunkSize || 2000;
        concurrentRequestsInput.value = config.concurrentRequests || 2;
        translationPercentageInput.value = config.translationPercentage || 100;
        percentageValueDisplay.textContent = `${translationPercentageInput.value}%`;
        translationModeSelect.value = config.translationMode || 'random';
        
        // 翻译样式设置
        translationStyleSelect.value = config.translationStyle || 'replace';
        underlineColorInput.value = config.underlineColor || '#4285f4';
        
        // 根据当前样式控制颜色选择器显示
        const showColorPicker = translationStyleSelect.value === 'underline';
        document.getElementById('underlineColor').parentElement.style.display = 
          showColorPicker ? 'flex' : 'none';
        
        // 隐藏所有模型选项
        document.querySelectorAll('.ai-model-options').forEach(el => {
          el.classList.remove('show');
        });
        
        // 设置正确的模型选择
        updateModelOptionsVisibility(config.aiService);
        
        // 根据所选AI服务设置正确的模型值
        switch(config.aiService) {
          case 'openai':
            openaiModelSelect.value = config.aiModel || 'gpt-3.5-turbo';
            debugLog('设置OpenAI模型', openaiModelSelect.value);
            break;
          case 'deepseek':
            deepseekModelSelect.value = config.aiModel || 'deepseek-chat';
            debugLog('设置Deepseek模型', deepseekModelSelect.value);
            break;
          case 'qwen':
            qwenModelSelect.value = config.aiModel || 'qwen-turbo';
            debugLog('设置通义千问模型', qwenModelSelect.value);
            break;
          case 'custom':
            customModelInput.value = config.aiModel || '';
            debugLog('设置自定义模型', customModelInput.value);
            break;
        }
        
        // 将配置信息添加到状态日志
        if (window.addStatusLog) {
          window.addStatusLog(`当前服务: ${config.aiService}`, 'info');
          window.addStatusLog(`当前模型: ${config.aiModel || '默认模型'}`, 'info');
        }
      }
    });
    
    // 获取当前标签页的翻译状态
    const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });
    
    if (activeTab) {
      chrome.tabs.sendMessage(
        activeTab.id, 
        { action: 'getStatus' }, 
        (response) => {
          if (response) {
            isTranslated = response.isTranslated;
            updateButtonState();
            statusElement.textContent = isTranslated ? '已翻译' : '原始页面';
          }
        }
      );
    }
  }
  
  // 显示正确的模型选项
  function updateModelOptionsVisibility(service) {
    // 隐藏所有模型选项
    document.querySelectorAll('.ai-model-options').forEach(el => {
      el.classList.remove('show');
    });
    
    // 显示对应服务的模型选项
    switch(service) {
      case 'openai':
        document.getElementById('openai-models').classList.add('show');
        break;
      case 'deepseek':
        document.getElementById('deepseek-models').classList.add('show');
        break;
      case 'qwen':
        document.getElementById('qwen-models').classList.add('show');
        break;
      case 'custom':
        document.getElementById('custom-model').classList.add('show');
        break;
      default:
        document.getElementById('openai-models').classList.add('show');
    }
  }
  
  // 更新按钮状态
  function updateButtonState() {
    translateButton.textContent = isTranslated ? '恢复原文' : '翻译此页面';
  }
  
  // 保存设置
  function saveSettings() {
    const newConfig = {
      defaultTargetLanguage: targetLanguageSelect.value,
      enableAutoTranslate: autoTranslateCheckbox.checked,
      apiEndpoint: apiEndpointInput.value,
      apiKey: apiKeyInput.value,
      aiService: aiServiceSelect.value,
      promptTemplate: promptTemplateInput.value,
      bypassModelValidation: bypassModelValidationCheckbox.checked,
      aiModel: getCurrentAiModelValue(),
      // 新增配置项
      maxChunkSize: parseInt(maxChunkSizeInput.value) || 2000,
      concurrentRequests: parseInt(concurrentRequestsInput.value) || 2,
      translationPercentage: parseInt(translationPercentageInput.value) || 100,
      translationMode: translationModeSelect.value,
      // 翻译样式配置
      translationStyle: translationStyleSelect.value,
      underlineColor: underlineColorInput.value
    };
    
    // 发送到后台保存
    chrome.runtime.sendMessage(
      { 
        action: 'updateConfig', 
        config: newConfig 
      }, 
      (response) => {
        if (response && response.success) {
          statusElement.textContent = '设置已保存';
          
          // 如果启用了绕过验证，显示提示
          if (bypassModelValidationCheckbox.checked) {
            window.addStatusLog('已启用模型验证绕过功能', 'warning');
            window.addStatusLog('您现在可以使用任何模型名称，但请确保模型名称正确', 'warning');
            
            // 隐藏模型警告
            document.getElementById('model-warning').style.display = 'none';
          }
          
          // 显示选择的模型信息
          const modelValue = getCurrentAiModelValue();
          window.addStatusLog(`已选择模型: ${modelValue}`, 'info');
          
          setTimeout(() => {
            statusElement.textContent = isTranslated ? '已翻译' : '原始页面';
          }, 1500);
        }
      }
    );
  }
  
  // 获取当前选择的AI模型值
  function getCurrentAiModelValue() {
    switch(aiServiceSelect.value) {
      case 'openai':
        return openaiModelSelect.value;
      case 'deepseek':
        return deepseekModelSelect.value;
      case 'qwen':
        return qwenModelSelect.value;
      case 'custom':
        return customModelInput.value;
      default:
        return '';
    }
  }
  
  // 保存AI模型设置
  function saveAIModel() {
    let aiModel = getCurrentAiModelValue();
    
    // 更新并保存配置
    chrome.runtime.sendMessage(
      { 
        action: 'updateConfig', 
        config: { aiModel: aiModel }
      }, 
      (response) => {
        if (response && response.success) {
          statusElement.textContent = '模型设置已保存';
          window.addStatusLog(`已更新模型: ${aiModel}`, 'info');
          setTimeout(() => {
            statusElement.textContent = isTranslated ? '已翻译' : '原始页面';
          }, 1500);
        }
      }
    );
  }

  // 显示状态消息
  function showStatus(message, type = 'info') {
    debugLog('显示状态消息', { message, type });
    const statusDiv = document.getElementById('status-message');
    if (statusDiv) {
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      statusDiv.style.display = 'block';
      
      // 3秒后自动隐藏
      setTimeout(() => {
        statusDiv.style.display = 'none';
      }, 3000);
    }
  }
}); 