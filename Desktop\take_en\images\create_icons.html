<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>生成翻译插件图标</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    
    h1 {
      color: #4285f4;
    }
    
    .preview {
      margin: 20px 0;
      text-align: center;
    }
    
    button {
      padding: 10px 20px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    
    .instructions {
      margin-top: 20px;
      padding: 15px;
      background-color: #f8f9fa;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>翻译插件图标生成器</h1>
  
  <div class="preview">
    <p>图标预览：</p>
    <canvas id="preview16" width="16" height="16" style="border:1px solid #ddd; margin: 5px;"></canvas>
    <canvas id="preview48" width="48" height="48" style="border:1px solid #ddd; margin: 5px;"></canvas>
    <canvas id="preview128" width="128" height="128" style="border:1px solid #ddd; margin: 5px;"></canvas>
  </div>
  
  <div style="text-align: center;">
    <button id="generateButton">生成并下载图标</button>
  </div>
  
  <div class="instructions">
    <h3>使用说明：</h3>
    <ol>
      <li>点击"生成并下载图标"按钮</li>
      <li>浏览器会自动下载三个图标文件：icon16.png, icon48.png, icon128.png</li>
      <li>将这些文件保存到插件的images目录中</li>
    </ol>
  </div>
  
  <script>
    // 预览绘制函数
    function drawPreview() {
      const canvas16 = document.getElementById('preview16');
      const ctx16 = canvas16.getContext('2d');
      
      const canvas48 = document.getElementById('preview48');
      const ctx48 = canvas48.getContext('2d');
      
      const canvas128 = document.getElementById('preview128');
      const ctx128 = canvas128.getContext('2d');
      
      // 绘制16x16图标
      ctx16.fillStyle = '#4285f4';
      ctx16.fillRect(0, 0, 16, 16);
      ctx16.fillStyle = 'white';
      ctx16.font = 'bold 10px Arial';
      ctx16.fillText('T', 5, 11);
      
      // 绘制48x48图标
      ctx48.fillStyle = '#4285f4';
      ctx48.fillRect(0, 0, 48, 48);
      ctx48.fillStyle = 'white';
      ctx48.font = 'bold 30px Arial';
      ctx48.fillText('T', 15, 33);
      
      // 绘制128x128图标
      ctx128.fillStyle = '#4285f4';
      ctx128.fillRect(0, 0, 128, 128);
      ctx128.fillStyle = 'white';
      ctx128.font = 'bold 80px Arial';
      ctx128.fillText('T', 40, 88);
    }
    
    // 生成并下载图标
    document.getElementById('generateButton').addEventListener('click', function() {
      // 创建Canvas元素
      const canvas16 = document.createElement('canvas');
      canvas16.width = 16;
      canvas16.height = 16;
      const ctx16 = canvas16.getContext('2d');
      
      const canvas48 = document.createElement('canvas');
      canvas48.width = 48;
      canvas48.height = 48;
      const ctx48 = canvas48.getContext('2d');
      
      const canvas128 = document.createElement('canvas');
      canvas128.width = 128;
      canvas128.height = 128;
      const ctx128 = canvas128.getContext('2d');
      
      // 绘制16x16图标
      ctx16.fillStyle = '#4285f4';
      ctx16.fillRect(0, 0, 16, 16);
      ctx16.fillStyle = 'white';
      ctx16.font = 'bold 10px Arial';
      ctx16.fillText('T', 5, 11);
      
      // 绘制48x48图标
      ctx48.fillStyle = '#4285f4';
      ctx48.fillRect(0, 0, 48, 48);
      ctx48.fillStyle = 'white';
      ctx48.font = 'bold 30px Arial';
      ctx48.fillText('T', 15, 33);
      
      // 绘制128x128图标
      ctx128.fillStyle = '#4285f4';
      ctx128.fillRect(0, 0, 128, 128);
      ctx128.fillStyle = 'white';
      ctx128.font = 'bold 80px Arial';
      ctx128.fillText('T', 40, 88);
      
      // 转换为DataURL
      const icon16DataURL = canvas16.toDataURL('image/png');
      const icon48DataURL = canvas48.toDataURL('image/png');
      const icon128DataURL = canvas128.toDataURL('image/png');
      
      // 创建下载链接
      const link16 = document.createElement('a');
      link16.download = 'icon16.png';
      link16.href = icon16DataURL;
      link16.click();
      
      const link48 = document.createElement('a');
      link48.download = 'icon48.png';
      link48.href = icon48DataURL;
      link48.click();
      
      const link128 = document.createElement('a');
      link128.download = 'icon128.png';
      link128.href = icon128DataURL;
      link128.click();
      
      alert('图标已生成并开始下载。请将下载的图标文件移动到插件的images文件夹中。');
    });
    
    // 初始化预览
    drawPreview();
  </script>
</body>
</html> 