// 后台服务脚本
// 负责与翻译API和AI服务通信

// 默认配置
let config = {
  apiEndpoint: 'https://api.example.com/translate', // 替换为实际的翻译API
  defaultTargetLanguage: 'zh-CN',
  enableAutoTranslate: false,
  apiKey: '',  // 如需API密钥可在此设置
  aiService: 'openai', // 默认使用OpenAI
  aiModel: 'deepseek-chat',  // 默认模型
  promptTemplate: "请将下面的文本从{sourceLanguage}翻译成{targetLanguage}，保持原文的格式和语气：\n\n{text}",
  bypassModelValidation: false
};

// 初始化时从存储中加载配置
chrome.storage.sync.get('translatorConfig', (data) => {
  if (data.translatorConfig) {
    config = { ...config, ...data.translatorConfig };
  }
});

// 翻译API配置
const GOOGLE_TRANSLATE_API = 'https://translation.googleapis.com/language/translate/v2';
const API_KEY = 'YOUR_API_KEY'; // 替换为您的API密钥

// 添加调试日志函数
function debugLog(message, data = null) {
  console.log(`[Translator Background Debug] ${message}`, data || '');
}

// 添加标签页创建/更新监听器，确保内容脚本已加载
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    debugLog('页面加载完成', { tabId, url: tab.url });
    
    // 尝试检查内容脚本是否已加载
    chrome.tabs.sendMessage(tabId, { action: 'ping' }, response => {
      if (chrome.runtime.lastError) {
        debugLog('内容脚本未加载，尝试注入', { tabId, error: chrome.runtime.lastError.message });
        
        // 如果内容脚本未加载，尝试手动注入
        chrome.scripting.executeScript({
          target: { tabId: tabId },
          files: ['content.js']
        })
        .then(() => {
          debugLog('内容脚本注入成功', { tabId });
          
          // 注入CSS
          chrome.scripting.insertCSS({
            target: { tabId: tabId },
            files: ['content.css']
          })
          .then(() => {
            debugLog('内容CSS注入成功', { tabId });
          })
          .catch(error => {
            debugLog('内容CSS注入失败', { tabId, error });
          });
        })
        .catch(error => {
          debugLog('内容脚本注入失败', { tabId, error });
        });
      } else {
        debugLog('内容脚本已加载', { tabId, response });
      }
    });
  }
});

// 监听来自内容脚本或popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  debugLog('收到消息', message);
  
  if (message.action === 'translateContent') {
    debugLog('处理翻译请求');
    
    // 将处理包装在try-catch中以防止未捕获的异常
    try {
      // 使用实际的AI翻译服务，而非模拟测试
      aiTranslateContent(
        message.content, 
        message.sourceLanguage || 'auto', 
        message.targetLanguage || config.defaultTargetLanguage,
        message, // 传递完整消息对象以获取delimiters标志
        (response) => {
          debugLog('翻译完成，返回结果', response);
          sendResponse(response);
        }
      );
      
      return true; // 表示将异步发送响应
    } catch (unexpectedError) {
      debugLog('处理请求时发生未预期的错误', unexpectedError);
      try {
        sendResponse({ 
          success: false, 
          error: '发生内部错误，请重试',
          details: unexpectedError.toString()
        });
      } catch (sendError) {
        debugLog('发送错误响应时出错', sendError);
      }
      return true;
    }
  } else if (message.action === 'updateConfig') {
    updateConfig(message.config, sendResponse);
    return false; // 同步响应
  } else if (message.action === 'getConfig') {
    sendResponse({ success: true, config: config });
    return false; // 同步响应
  }
});

// 翻译内容
async function translateContent(content, sourceLanguage = 'auto', targetLanguage = 'zh-CN') {
  debugLog('开始翻译', { contentLength: content.length, sourceLanguage, targetLanguage });
  
  try {
    // 这里实现实际的翻译逻辑
    // 这是一个模拟实现，实际应用中应当调用真实的翻译API
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 调用翻译API
    debugLog('调用翻译API');
    
    try {
      // 实际调用Google翻译API的示例
      // const response = await fetch(`${GOOGLE_TRANSLATE_API}?key=${API_KEY}`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     q: content,
      //     source: sourceLanguage,
      //     target: targetLanguage,
      //     format: 'html'
      //   })
      // });
      
      // const data = await response.json();
      // if (!response.ok) {
      //   throw new Error(data.error ? data.error.message : '翻译API错误');
      // }
      
      // return data.data.translations[0].translatedText;
      
      // 简单模拟翻译结果（实际应用中请替换为真实API调用）
      debugLog('模拟翻译完成');
      return `<div class="translated-content">
                <p>这是翻译后的内容示例。原始内容长度: ${content.length} 字符。</p>
                <p>在实际应用中，这里应该是通过翻译API返回的翻译后内容。</p>
                <hr>
                <p>${content.substring(0, 200)}...</p>
              </div>`;
    } catch (apiError) {
      debugLog('API调用错误', apiError);
      throw new Error(`翻译API错误: ${apiError.message}`);
    }
  } catch (error) {
    debugLog('翻译过程出错', error);
    throw error;
  }
}

// 使用AI服务翻译内容 - 增强版，添加详细日志和模型验证
async function aiTranslateContent(text, sourceLanguage, targetLanguage, message, callback) {
  try {
    let response;
    let translatedText;
    let requestData = null; // 存储请求数据
    
    // 检查是否有分隔符标记
    const useDelimiters = message && message.delimiters === true;
    
    // 创建提示词
    const prompt = config.promptTemplate
      .replace('{sourceLanguage}', sourceLanguage === 'auto' ? '任何语言' : sourceLanguage)
      .replace('{targetLanguage}', targetLanguage)
      .replace('{text}', text);
    
    debugLog('翻译提示词', { 
      prompt: prompt.substring(0, 100) + '...',
      useDelimiters: useDelimiters,
      textLength: text.length
    });
    
    // 记录开始时间
    const startTime = new Date();
    
    // 验证选择的模型
    const validationResult = validateModel(config.aiService, config.aiModel);
    if (!validationResult.valid) {
      debugLog('模型验证失败', validationResult);
      throw new Error(`模型验证失败: ${validationResult.message}`);
    }
    
    // 如果使用分隔符，添加额外的指导
    let systemPrompt = '你是一个专业的翻译助手，只需要翻译用户提供的文本，不需要解释或回答问题。';
    
    if (useDelimiters) {
      systemPrompt += ' 请逐行翻译文本，保持原始格式和换行符。确保输出与输入的行数完全相同，每行文本都要翻译。不要合并或拆分行。';
    }
    
    switch (config.aiService) {
      case 'openai':
        debugLog('使用OpenAI服务', { model: config.aiModel || 'gpt-3.5-turbo' });
        
        // 构建请求体
        const openaiRequestBody = {
          model: config.aiModel || 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: prompt }
          ],
          temperature: 0.3
        };
        
        // 存储请求数据
        requestData = {
          url: 'https://api.openai.com/v1/chat/completions',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${maskApiKey(config.apiKey)}`
          },
          body: openaiRequestBody
        };
        
        debugLog('OpenAI请求详情', requestData);
        
        try {
          const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify(openaiRequestBody)
          });
          
          // 检查响应状态
          if (!openaiResponse.ok) {
            const errorText = await openaiResponse.text();
            debugLog('OpenAI API响应错误', { 
              status: openaiResponse.status, 
              statusText: openaiResponse.statusText,
              error: errorText 
            });
            throw new Error(`OpenAI API响应错误: ${openaiResponse.status} ${errorText}`);
          }
          
          // 解析响应
          response = await openaiResponse.json();
          debugLog('OpenAI响应', response);
          translatedText = response.choices[0].message.content;
        } catch (error) {
          debugLog('OpenAI API调用出错', error);
          throw error;
        }
        break;
        
      case 'deepseek':
        debugLog('使用Deepseek服务', { model: config.aiModel || 'deepseek-chat' });
        
        // 构建请求体
        const deepseekRequestBody = {
          model: config.aiModel || 'deepseek-chat',
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: prompt }
          ],
          temperature: 0.3
        };
        
        // 存储请求数据
        requestData = {
          url: 'https://api.deepseek.com/v1/chat/completions',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${maskApiKey(config.apiKey)}`
          },
          body: deepseekRequestBody
        };
        
        debugLog('Deepseek请求详情', requestData);
        
        try {
          const deepseekResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify(deepseekRequestBody)
          });
          
          // 检查响应状态
          if (!deepseekResponse.ok) {
            const errorText = await deepseekResponse.text();
            debugLog('Deepseek API响应错误', { 
              status: deepseekResponse.status, 
              statusText: deepseekResponse.statusText,
              error: errorText 
            });
            throw new Error(`Deepseek API响应错误: ${deepseekResponse.status} ${errorText}`);
          }
          
          // 解析响应
          response = await deepseekResponse.json();
          debugLog('Deepseek响应', response);
          translatedText = response.choices[0].message.content;
        } catch (error) {
          debugLog('Deepseek API调用出错', error);
          throw error;
        }
        break;
        
      case 'qwen':
        debugLog('使用通义千问服务', { model: config.aiModel || 'qwen-turbo' });
        
        // 构建请求体
        const qwenRequestBody = {
          model: config.aiModel || 'qwen-turbo',
          input: {
            messages: [
              { role: 'system', content: systemPrompt },
              { role: 'user', content: prompt }
            ]
          },
          parameters: {
            temperature: 0.3
          }
        };
        
        // 存储请求数据
        requestData = {
          url: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${maskApiKey(config.apiKey)}`
          },
          body: qwenRequestBody
        };
        
        debugLog('通义千问请求详情', requestData);
        
        try {
          const qwenResponse = await fetch('https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify(qwenRequestBody)
          });
          
          // 检查响应状态
          if (!qwenResponse.ok) {
            const errorText = await qwenResponse.text();
            debugLog('通义千问 API响应错误', { 
              status: qwenResponse.status, 
              statusText: qwenResponse.statusText,
              error: errorText 
            });
            throw new Error(`通义千问 API响应错误: ${qwenResponse.status} ${errorText}`);
          }
          
          // 解析响应
          response = await qwenResponse.json();
          debugLog('通义千问响应', response);
          translatedText = response.output.text;
        } catch (error) {
          debugLog('通义千问 API调用出错', error);
          throw error;
        }
        break;
        
      case 'mock':
        debugLog('使用模拟翻译服务', { useDelimiters });
        
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 简单的模拟翻译逻辑
        if (useDelimiters) {
          // 如果使用分隔符，逐行翻译
          const lines = text.split('\n');
          const translatedLines = lines.map(line => {
            // 简单的模拟翻译：在每行前添加目标语言的前缀
            const langPrefix = targetLanguage === 'zh-CN' ? '【中】' : 
                             targetLanguage === 'en' ? '【EN】' : 
                             `【${targetLanguage}】`;
            return `${langPrefix} ${line}`;
          });
          translatedText = translatedLines.join('\n');
        } else {
          // 简单翻译整个文本
          const langPrefix = targetLanguage === 'zh-CN' ? '【模拟中文翻译】' : 
                           targetLanguage === 'en' ? '【Mock English Translation】' : 
                           `【Mock ${targetLanguage} Translation】`;
          translatedText = `${langPrefix} ${text}`;
        }
        
        // 创建模拟响应
        response = {
          success: true,
          mock: true,
          service: 'mock',
          model: 'mock-model',
          translatedContent: translatedText
        };
        
        debugLog('模拟翻译响应', response);
        break;
        
      case 'custom':
        debugLog('使用自定义API', { endpoint: config.apiEndpoint, model: config.aiModel });
        
        // 构建请求体
        const customRequestBody = {
          prompt: prompt,
          model: config.aiModel,
          temperature: 0.3,
          system_prompt: systemPrompt,
          use_delimiters: useDelimiters
        };
        
        // 存储请求数据
        requestData = {
          url: config.apiEndpoint,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': config.apiKey ? `Bearer ${maskApiKey(config.apiKey)}` : undefined
          },
          body: customRequestBody
        };
        
        debugLog('自定义API请求详情', requestData);
        
        try {
          const customResponse = await fetch(config.apiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': config.apiKey ? `Bearer ${config.apiKey}` : undefined
            },
            body: JSON.stringify(customRequestBody)
          });
          
          // 检查响应状态
          if (!customResponse.ok) {
            const errorText = await customResponse.text();
            debugLog('自定义API响应错误', { 
              status: customResponse.status, 
              statusText: customResponse.statusText,
              error: errorText 
            });
            throw new Error(`自定义API响应错误: ${customResponse.status} ${errorText}`);
          }
          
          // 解析响应
          response = await customResponse.json();
          debugLog('自定义API响应', response);
          translatedText = response.result || response.translatedText || response.text;
        } catch (error) {
          debugLog('自定义API调用出错', error);
          throw error;
        }
        break;
        
      default:
        throw new Error('不支持的AI服务');
    }
    
    // 计算耗时
    const endTime = new Date();
    const duration = (endTime - startTime) / 1000;
    
    debugLog('翻译完成', { 
      service: config.aiService,
      duration: `${duration}秒`,
      sourceLength: text.length,
      translatedLength: translatedText.length
    });
    
    // 如果使用分隔符模式，直接返回翻译文本而不是HTML包装
    if (useDelimiters) {
      callback({ 
        success: true, 
        translatedContent: translatedText, // 直接返回翻译文本
        originalResponse: response,
        requestData: requestData, // 添加请求数据
        metadata: {
          service: config.aiService,
          model: config.aiModel,
          duration: duration,
          timestamp: new Date().toISOString()
        }
      });
      return;
    }
    
    // 对于整页翻译，包装翻译后的文本为HTML
    const wrappedHTML = `
    <div class="translated-content">
      <style>
        .translated-content {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 20px;
        }
        .translated-content .meta {
          color: #666;
          font-size: 0.9em;
          margin-bottom: 20px;
          padding: 10px;
          background: #f5f5f5;
          border-radius: 4px;
        }
        .translated-content .content {
          white-space: pre-wrap;
        }
      </style>
      <div class="meta">
        <p>翻译完成，用时: ${duration}秒</p>
        <p>翻译服务: ${config.aiService} (${config.aiModel || '默认模型'})</p>
      </div>
      <div class="content">
        ${translatedText}
      </div>
    </div>
    `;
    
    callback({ 
      success: true, 
      translatedContent: wrappedHTML,
      originalResponse: response,
      requestData: requestData, // 添加请求数据
      metadata: {
        service: config.aiService,
        model: config.aiModel,
        duration: duration,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    debugLog('翻译出错:', error);
    callback({ 
      success: false, 
      error: error.message || '翻译服务连接失败',
      details: error.toString()
    });
  }
}

// 掩盖API密钥，只显示前4个和后4个字符
function maskApiKey(apiKey) {
  if (!apiKey || apiKey.length < 10) return '[API密钥已隐藏]';
  return apiKey.substring(0, 4) + '...' + apiKey.substring(apiKey.length - 4);
}

// 验证模型函数
function validateModel(service, model) {
  debugLog('验证模型', { service, model });
  
  // 检查是否应该绕过模型验证
  if (config.bypassModelValidation) {
    return { 
      valid: true, 
      message: '已绕过模型验证' 
    };
  }
  
  // 如果没有提供模型，返回默认模型
  if (!model) {
    switch (service) {
      case 'openai':
        return { valid: true, message: '使用默认模型: gpt-3.5-turbo' };
      case 'deepseek':
        return { valid: true, message: '使用默认模型: deepseek-chat' };
      case 'qwen':
        return { valid: true, message: '使用默认模型: qwen-turbo' };
      case 'custom':
        return { valid: false, message: '自定义服务需要指定模型名称' };
      default:
        return { valid: false, message: '不支持的AI服务' };
    }
  }
  
  // 验证各服务的模型
  switch (service) {
    case 'openai':
      // OpenAI模型列表
      const openaiModels = [
        'gpt-3.5-turbo', 'gpt-3.5-turbo-16k', 'gpt-4', 'gpt-4-turbo', 'gpt-4-32k',
        'gpt-4o', 'gpt-4o-mini'
      ];
      return { 
        valid: openaiModels.includes(model), 
        message: openaiModels.includes(model) ? '有效的OpenAI模型' : '未知的OpenAI模型，可能导致API错误'
      };
      
    case 'deepseek':
      // Deepseek模型列表
      const deepseekModels = [
        'deepseek-chat', 'deepseek-reasoner'
      ];
      // 检查模型名称是否以deepseek开头，如果是，也认为是有效的
      const isDeepseekModel = model.toLowerCase().includes('deepseek');
      return { 
        valid: deepseekModels.includes(model) || isDeepseekModel, 
        message: deepseekModels.includes(model) || isDeepseekModel ? 
          '有效的Deepseek模型' : 
          '未知的Deepseek模型，可能导致API错误' + model
      };
      
    case 'qwen':
      // 通义千问模型列表
      const qwenModels = [
        'qwen-turbo', 'qwen-plus', 'qwen-max', 'qwen-max-longcontext',
        'qwen-vl-plus', 'qwen-vl-max'
      ];
      return { 
        valid: qwenModels.includes(model), 
        message: qwenModels.includes(model) ? '有效的通义千问模型' : '未知的通义千问模型，可能导致API错误'
      };
      
    case 'custom':
      // 自定义模型仅检查是否提供了名称
      return { 
        valid: model.trim().length > 0, 
        message: model.trim().length > 0 ? '已提供自定义模型名称' : '未提供自定义模型名称'
      };
      
    default:
      return { valid: false, message: '不支持的AI服务' };
  }
}

// 更新配置
function updateConfig(newConfig, callback) {
  config = { ...config, ...newConfig };
  
  // 保存到存储
  chrome.storage.sync.set({ 'translatorConfig': config }, () => {
    callback({ success: true });
  });
} 
