<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>网页翻译</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
      width: 320px;
      padding: 15px;
      background-color: #f8f9fa;
    }
    
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
    
    .logo {
      width: 24px;
      height: 24px;
    }
    
    .main-button {
      width: 100%;
      padding: 10px;
      background-color: #4285f4;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
      margin-bottom: 15px;
    }
    
    .main-button:hover {
      background-color: #3b78e7;
    }
    
    .main-button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    
    .status {
      text-align: center;
      margin-bottom: 15px;
      color: #666;
      font-size: 13px;
    }
    
    .settings {
      background-color: white;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .setting-item {
      margin-bottom: 12px;
    }
    
    .setting-item:last-child {
      margin-bottom: 0;
    }
    
    .setting-label {
      display: block;
      margin-bottom: 5px;
      font-size: 13px;
      color: #333;
    }
    
    select, input {
      width: 100%;
      padding: 6px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      box-sizing: border-box;
    }
    
    .footer {
      margin-top: 15px;
      text-align: center;
      font-size: 12px;
      color: #888;
    }
    
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 40px;
      height: 20px;
    }
    
    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 20px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 16px;
      width: 16px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #4285f4;
    }
    
    input:checked + .slider:before {
      transform: translateX(20px);
    }
    
    .setting-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tabs {
      display: flex;
      margin-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }

    .tab {
      padding: 8px 12px;
      cursor: pointer;
      font-size: 13px;
      border-bottom: 2px solid transparent;
    }

    .tab.active {
      border-bottom: 2px solid #4285f4;
      color: #4285f4;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .ai-model-options {
      display: none;
    }

    .ai-model-options.show {
      display: block;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1 class="title">网页翻译</h1>
    <img src="images/icon48.png" class="logo" alt="翻译图标">
  </div>
  
  <button id="translateButton" class="main-button">翻译此页面</button>
  
  <div id="status" class="status">准备就绪</div>
  
  <div class="tabs">
    <div class="tab active" data-tab="basic">基本设置</div>
    <div class="tab" data-tab="advanced">高级设置</div>
  </div>
  
  <div class="settings">
    <div id="basic-tab" class="tab-content active">
      <div class="setting-item">
        <label class="setting-label">目标语言</label>
        <select id="targetLanguage">
          <option value="zh-CN">简体中文</option>
          <option value="en">英语</option>
          <option value="ja">日语</option>
          <option value="ko">韩语</option>
          <option value="fr">法语</option>
          <option value="de">德语</option>
          <option value="es">西班牙语</option>
          <option value="ru">俄语</option>
        </select>
      </div>
      
      <div class="setting-item">
        <div class="setting-row">
          <label class="setting-label">自动翻译</label>
          <label class="toggle-switch">
            <input type="checkbox" id="autoTranslate">
            <span class="slider"></span>
          </label>
        </div>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">AI服务</label>
        <select id="aiService">
          <option value="openai">OpenAI</option>
          <option value="deepseek">Deepseek</option>
          <option value="qwen">通义千问</option>
          <option value="custom">自定义API</option>
          <option value="mock">模拟翻译(测试用)</option>
        </select>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">API密钥</label>
        <input type="password" id="apiKey" placeholder="输入API密钥">
      </div>
    </div>
    
    <div id="advanced-tab" class="tab-content">
      <div class="setting-item">
        <label class="setting-label">AI模型选择</label>
        
        <div id="openai-models" class="ai-model-options">
          <select id="openaiModel">
            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
            <option value="gpt-3.5-turbo-16k">GPT-3.5 Turbo 16K</option>
            <option value="gpt-4">GPT-4</option>
            <option value="gpt-4-turbo">GPT-4 Turbo</option>
            <option value="gpt-4-32k">GPT-4 32K</option>
            <option value="gpt-4o">GPT-4o</option>
            <option value="gpt-4o-mini">GPT-4o Mini</option>
          </select>
          <div class="setting-info" style="margin-top: 5px; font-size: 12px; color: #666;">
            请确保您的API密钥具有所选模型的访问权限。
          </div>
        </div>
        
        <div id="deepseek-models" class="ai-model-options">
          <select id="deepseekModel">
            <option value="deepseek-chat">Deepseek Chat</option>
            <option value="deepseek-coder">Deepseek Coder</option>
            <option value="deepseek-llm-7b-chat">Deepseek LLM 7B Chat</option>
            <option value="deepseek-llm-67b-chat">Deepseek LLM 67B Chat</option>
            <option value="deepseek-coder-6.7b-instruct">Deepseek Coder 6.7B Instruct</option>
            <option value="deepseek-coder-33b-instruct">Deepseek Coder 33B Instruct</option>
            <option value="deepseek-math-7b-instruct">Deepseek Math 7B Instruct</option>
            <option value="deepseek-vl-7b-chat">Deepseek VL 7B Chat</option>
          </select>
          <div class="setting-info" style="margin-top: 5px; font-size: 12px; color: #666;">
            请确保您选择的模型在Deepseek API中可用。如果不确定，请参考<a href="https://platform.deepseek.com/" target="_blank">Deepseek平台文档</a>。
          </div>
        </div>
        
        <div id="qwen-models" class="ai-model-options">
          <select id="qwenModel">
            <option value="qwen-turbo">通义千问-Turbo</option>
            <option value="qwen-plus">通义千问-Plus</option>
            <option value="qwen-max">通义千问-Max</option>
            <option value="qwen-max-longcontext">通义千问-Max长文本</option>
            <option value="qwen-vl-plus">通义千问-VL Plus</option>
            <option value="qwen-vl-max">通义千问-VL Max</option>
          </select>
          <div class="setting-info" style="margin-top: 5px; font-size: 12px; color: #666;">
            请参考<a href="https://help.aliyun.com/zh/dashscope/" target="_blank">通义千问文档</a>获取最新模型信息。
          </div>
        </div>
        
        <div id="custom-model" class="ai-model-options">
          <input type="text" id="customModel" placeholder="输入模型名称">
          <div class="setting-info" style="margin-top: 5px; font-size: 12px; color: #666;">
            输入您自定义API接受的模型名称。确保与API文档中的模型名称完全匹配。
          </div>
        </div>

        <div id="model-warning" style="display: none; margin-top: 10px; padding: 8px; background-color: #fff3cd; border: 1px solid #ffeeba; border-radius: 4px; color: #856404; font-size: 12px;">
          提示：如果遇到"Model Not Exist"错误，请确保选择正确的模型名称，并参考API提供商的文档。
        </div>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">自定义API端点</label>
        <input type="text" id="apiEndpoint" placeholder="https://api.example.com/translate">
      </div>
      
      <div class="setting-item">
        <label class="setting-label">提示词模板</label>
        <textarea id="promptTemplate" rows="4" style="width: 100%; resize: vertical;">请将下面的文本从{sourceLanguage}翻译成{targetLanguage}，保持原文的格式和语气：

{text}</textarea>
      </div>

      <div class="setting-item">
        <div class="setting-row">
          <label class="setting-label">绕过模型验证 <span style="color: #e53935; font-size: 11px;">(高级用户)</span></label>
          <label class="toggle-switch">
            <input type="checkbox" id="bypassModelValidation">
            <span class="slider"></span>
          </label>
        </div>
        <div class="setting-info" style="margin-top: 5px; font-size: 12px; color: #666;">
          启用此选项可以使用任何模型名称，包括未在预设列表中的模型。仅在确定模型名称正确时使用。
        </div>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">长文本处理</label>
        <div class="setting-info" style="margin-bottom: 10px; font-size: 12px; color: #666;">
          设置如何处理长文本内容，以避免超出API限制
        </div>
        
        <div class="setting-row" style="margin-bottom: 10px;">
          <label class="setting-label">最大块大小(字符)</label>
          <input type="number" id="maxChunkSize" min="100" max="10000" value="2000" style="width: 100px;">
        </div>
        
        <div class="setting-row">
          <label class="setting-label">同时翻译请求数</label>
          <input type="number" id="concurrentRequests" min="1" max="5" value="2" style="width: 100px;">
        </div>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">翻译比例控制</label>
        <div class="setting-info" style="margin-bottom: 10px; font-size: 12px; color: #666;">
          控制页面中有多少比例的内容被翻译
        </div>
        
        <div class="setting-row">
          <label class="setting-label">翻译比例(%)</label>
          <input type="range" id="translationPercentage" min="1" max="100" value="100" style="width: 100%;">
        </div>
        
        <div class="setting-row" style="justify-content: space-between; margin-top: 5px;">
          <span id="percentageValue" style="font-size: 12px;">100%</span>
          <select id="translationMode" style="width: auto;">
            <option value="random">随机选择短语</option>
            <option value="top">从顶部开始</option>
            <option value="important">优先重要内容</option>
          </select>
        </div>
      </div>
      
      <div class="setting-item">
        <label class="setting-label">翻译样式</label>
        <div class="setting-info" style="margin-bottom: 10px; font-size: 12px; color: #666;">
          设置翻译后内容的显示方式
        </div>
        
        <div class="setting-row">
          <select id="translationStyle" style="width: 100%;">
            <option value="replace">直接替换原文</option>
            <option value="underline">翻译带下划线+括号原文</option>
            <option value="tooltip">鼠标悬停显示原文</option>
          </select>
        </div>
        
        <div class="setting-row" style="margin-top: 10px;">
          <label class="setting-label">下划线颜色</label>
          <input type="color" id="underlineColor" value="#4285f4" style="width: 50px;">
        </div>
      </div>
    </div>
  </div>
  
  <div class="footer">
    网页翻译插件 v1.1
  </div>
  
  <script src="popup.js"></script>
</body>
</html> 