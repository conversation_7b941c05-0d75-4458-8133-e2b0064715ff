// 插件初始化
let isTranslated = false;
let originalContent = null;
let translatedContent = null;
// 存储原始节点内容的映射，用于恢复
let originalTextMap = new Map();

// 添加调试日志函数
function debugLog(message, data = null) {
  // 只在控制台输出日志，不再显示在页面上
  console.log(`[Translator Debug] ${message}`, data || '');
  
  // 不再调用showDebugInfo，注释掉这行
  // showDebugInfo(`[${new Date().toLocaleTimeString()}] ${message}`, data);
}

// 在页面加载时输出确认消息
debugLog('内容脚本已加载', { url: window.location.href });

// 手动测试翻译功能的按钮 - 已禁用
function addTestButton() {
  // 禁用测试按钮
  return;
  
  /*
  const testButton = document.createElement('button');
  testButton.textContent = '测试翻译功能';
  testButton.style.position = 'fixed';
  testButton.style.top = '50px';
  testButton.style.right = '10px';
  testButton.style.zIndex = '10001';
  testButton.style.padding = '5px 10px';
  testButton.style.backgroundColor = '#4285f4';
  testButton.style.color = 'white';
  testButton.style.border = 'none';
  testButton.style.borderRadius = '4px';
  testButton.style.cursor = 'pointer';
  
  testButton.onclick = () => {
    debugLog('手动触发翻译');
    translatePage();
  };
  
  document.body.appendChild(testButton);
  debugLog('添加了测试按钮');
  */
}

// 不再添加测试按钮
// setTimeout(addTestButton, 1000);

// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  debugLog('收到消息', message);
  
  if (message.action === 'ping') {
    // 响应ping消息，确认内容脚本已加载
    debugLog('收到ping消息，确认内容脚本已加载');
    sendResponse({ success: true, status: 'content_script_ready' });
  } else if (message.action === 'translate') {
    debugLog('执行翻译操作');
    if (!isTranslated) {
      translatePage();
    } else {
      restoreOriginal();
    }
    sendResponse({ success: true, isTranslated: isTranslated });
  } else if (message.action === 'getStatus') {
    debugLog('获取状态', { isTranslated });
    sendResponse({ isTranslated: isTranslated });
  }
  return true;
});

// 翻译页面
function translatePage() {
  // 向控制台输出非常明显的开始信息
  console.log('%c网页翻译已启动', 'color: white; background-color: #4285f4; font-size: 20px; padding: 10px; border-radius: 5px;');
  
  debugLog('开始翻译页面');
  // 保存原始内容
  if (!originalContent) {
    originalContent = document.body.innerHTML;
    debugLog('已保存原始内容');
  }
  
  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) {
      debugLog('获取配置失败', response);
      showTranslationError('获取配置失败');
      return;
    }
    
    const config = response.config;
    debugLog('获取配置成功', config);
    
    // 为用户打印明显的配置信息
    console.log('%c翻译配置', 'font-weight: bold; color: #0f9d58;');
    console.log(`翻译样式: ${config.translationStyle || 'replace'}`);
    console.log(`翻译比例: ${config.translationPercentage || 100}%`);
    console.log(`翻译模式: ${config.translationMode || 'random'}`);
    console.log(`最大块大小: ${config.maxChunkSize || 2000}字符`);
    console.log(`并发请求数: ${config.concurrentRequests || 2}`);
    
    // 显示加载中提示
    showTranslationLoading();
    
    // 提取页面文本节点
    const textNodes = extractTextNodes(document.body);
    debugLog(`提取到${textNodes.length}个文本节点`);
    console.log(`[网页翻译] 共提取到${textNodes.length}个文本节点`);
    
    // 按比例选择需要翻译的节点
    const nodesToTranslate = selectNodesToTranslate(textNodes, config);
    debugLog(`将翻译${nodesToTranslate.length}个文本节点（${config.translationPercentage}%）`);
    console.log(`[网页翻译] 将翻译${nodesToTranslate.length}个文本节点（${config.translationPercentage}%）`);
    
    // 打印一些示例节点内容
    console.log('%c要翻译的内容示例:', 'font-weight: bold;');
    nodesToTranslate.slice(0, 3).forEach((item, i) => {
      console.log(`示例${i+1}: ${item.text.substring(0, 100)}${item.text.length > 100 ? '...' : ''}`);
    });
    
    if (nodesToTranslate.length === 0) {
      showTranslationError('没有找到可翻译的文本');
      return;
    }
    
    // 分块处理文本
    const textChunks = splitIntoChunks(nodesToTranslate, config.maxChunkSize);
    debugLog(`文本已分为${textChunks.length}个块进行处理`);
    console.log(`[网页翻译] 文本已分为${textChunks.length}个块进行处理`);
    
    // 显示进度信息
    updateLoadingOverlay(`准备翻译 (0/${textChunks.length})`);
    
    // 使用Promise处理所有翻译任务
    translateChunks(textChunks, config)
      .then(results => {
        // 应用翻译结果到页面
        applyTranslationResults(nodesToTranslate, results)
          .then(appliedCount => {
            console.log(`%c翻译完成! 成功应用了${appliedCount}个翻译`, 'color: white; background-color: #0f9d58; font-size: 14px; padding: 5px; border-radius: 3px;');
            
            isTranslated = true;
            // 不再替换整个页面，而是显示翻译状态指示
            showTranslationStatus("部分内容已翻译");
          })
          .catch(error => {
            debugLog('翻译过程出错', error);
            showTranslationError(error.message || '翻译失败');
          });
      })
      .catch(error => {
        debugLog('翻译过程出错', error);
        showTranslationError(error.message || '翻译失败');
      });
  });
}

// 提取页面中的所有文本节点
function extractTextNodes(node) {
  const textNodes = [];
  
  // 递归函数来遍历DOM树
  function traverse(node) {
    // 如果是文本节点且包含非空文本
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim();
      if (text.length > 0) {
        // 对于长文本，尝试按句子分割
        if (text.length > 100) {
          splitTextIntoSentences(node, textNodes);
        } else {
          textNodes.push({
            node: node,
            text: text,
            length: text.length,
            parentTag: node.parentNode.tagName,
            // 基本权重：标题 > 段落 > 其他
            importance: getNodeImportance(node)
          });
        }
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 跳过脚本、样式和隐藏元素
      const style = window.getComputedStyle(node);
      if (node.tagName !== 'SCRIPT' && 
          node.tagName !== 'STYLE' && 
          style.display !== 'none' && 
          style.visibility !== 'hidden') {
        // 遍历子节点
        for (let i = 0; i < node.childNodes.length; i++) {
          traverse(node.childNodes[i]);
        }
      }
    }
  }
  
  // 开始遍历
  traverse(node);
  return textNodes;
}

// 将文本节点按句子分割
function splitTextIntoSentences(node, textNodes) {
  const text = node.textContent;
  
  // 如果文本太短，直接使用整个节点
  if (text.length < 100) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
    return;
  }
  
  // 创建一个临时容器来存放分割的句子
  const tempContainer = document.createElement('div');
  tempContainer.style.display = 'none';
  document.body.appendChild(tempContainer);
  
  // 分割文本为句子 - 使用更强大的正则表达式
  // 匹配英文和中文的句子结束符
  const sentencePattern = /[^.!?。！？]+[.!?。！？]+/g;
  const sentences = text.match(sentencePattern);
  
  // 如果无法分割成句子，使用更简单的方法按段落分割
  if (!sentences || sentences.length === 0) {
    // 按段落或短语分割
    const parts = text.split(/[,.;，。；]/);
    
    // 只有有多个部分时才分割
    if (parts.length > 1) {
      parts.forEach(part => {
        const trimmedPart = part.trim();
        if (trimmedPart.length > 0) {
          const sentenceNode = document.createTextNode(trimmedPart);
          tempContainer.appendChild(sentenceNode);
          tempContainer.appendChild(document.createElement('br'));
          
          textNodes.push({
            node: sentenceNode,
            text: trimmedPart,
            length: trimmedPart.length,
            parentTag: node.parentNode.tagName,
            importance: getNodeImportance(node),
            isVirtual: true,
            originalNode: node
          });
        }
      });
    } else {
      // 如果只有一个部分，使用整个节点
      textNodes.push({
        node: node,
        text: text,
        length: text.length,
        parentTag: node.parentNode.tagName,
        importance: getNodeImportance(node)
      });
    }
  } else {
    // 处理识别出的句子
    sentences.forEach(sentence => {
      const trimmedSentence = sentence.trim();
      if (trimmedSentence.length > 0) {
        const sentenceNode = document.createTextNode(trimmedSentence);
        tempContainer.appendChild(sentenceNode);
        tempContainer.appendChild(document.createElement('br'));
        
        textNodes.push({
          node: sentenceNode,
          text: trimmedSentence,
          length: trimmedSentence.length,
          parentTag: node.parentNode.tagName,
          importance: getNodeImportance(node),
          isVirtual: true,
          originalNode: node
        });
      }
    });
    
    // 检查是否有未匹配的文本（没有句号的结尾）
    const lastMatch = sentences[sentences.length - 1];
    const lastMatchEnd = text.lastIndexOf(lastMatch) + lastMatch.length;
    
    if (lastMatchEnd < text.length) {
      const remainingText = text.substring(lastMatchEnd).trim();
      if (remainingText.length > 0) {
        const remainingNode = document.createTextNode(remainingText);
        tempContainer.appendChild(remainingNode);
        
        textNodes.push({
          node: remainingNode,
          text: remainingText,
          length: remainingText.length,
          parentTag: node.parentNode.tagName,
          importance: getNodeImportance(node),
          isVirtual: true,
          originalNode: node
        });
      }
    }
  }
  
  // 移除临时容器
  document.body.removeChild(tempContainer);
  
  // 如果没有添加任何节点（分割失败），则使用原始节点
  if (!textNodes.some(item => item.originalNode === node)) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
  }
}

// 获取节点的重要性分数
function getNodeImportance(node) {
  const parent = node.parentNode;
  if (!parent) return 1;
  
  const tagName = parent.tagName;
  
  // 根据标签类型分配权重
  switch(tagName) {
    case 'H1': return 10;
    case 'H2': return 9;
    case 'H3': return 8;
    case 'H4': return 7;
    case 'H5': return 6;
    case 'H6': return 5;
    case 'P': return 4;
    case 'LI': return 3;
    case 'DIV': return 2;
    default: return 1;
  }
}

// 根据翻译比例选择需要翻译的节点
function selectNodesToTranslate(textNodes, config) {
  if (!textNodes.length) return [];
  
  const percentage = config.translationPercentage / 100;
  const mode = config.translationMode || 'random';
  
  // 如果是100%，直接返回所有节点
  if (percentage >= 1) {
    return textNodes;
  }
  
  // 计算需要翻译的节点数量
  const targetCount = Math.max(1, Math.floor(textNodes.length * percentage));
  
  switch(mode) {
    case 'random':
      // 随机选择节点
      return shuffleArray(textNodes).slice(0, targetCount);
      
    case 'top':
      // 从顶部开始选择
      return textNodes.slice(0, targetCount);
      
    case 'important':
      // 按重要性排序后选择
      return textNodes
        .sort((a, b) => b.importance - a.importance)
        .slice(0, targetCount);
        
    default:
      return shuffleArray(textNodes).slice(0, targetCount);
  }
}

// 辅助函数：随机打乱数组
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

// 将文本节点分成块，以便批量翻译
function splitIntoChunks(textNodes, maxChunkSize) {
  const chunks = [];
  let currentChunk = [];
  let currentSize = 0;
  
  for (const item of textNodes) {
    // 如果当前块已达到最大大小，或者添加此项会超过最大大小
    if (currentSize > 0 && (currentSize + item.text.length > maxChunkSize)) {
      // 保存当前块并开始新块
      chunks.push({
        nodes: currentChunk,
        text: currentChunk.map(n => n.text).join('\n'),
        size: currentSize
      });
      currentChunk = [];
      currentSize = 0;
    }
    
    // 添加到当前块
    currentChunk.push(item);
    currentSize += item.text.length;
  }
  
  // 添加最后一个块
  if (currentChunk.length > 0) {
    chunks.push({
      nodes: currentChunk,
      text: currentChunk.map(n => n.text).join('\n'),
      size: currentSize
    });
  }
  
  return chunks;
}

// 更新加载覆盖层的内容
function updateLoadingOverlay(message) {
  const overlay = document.getElementById('translation-overlay');
  const progressInfo = document.getElementById('translation-progress');
  
  if (overlay) {
    // 更新主标题如果提供
    if (message.includes('(')) {
      const textSpan = overlay.querySelector('span');
      if (textSpan) {
        textSpan.textContent = '翻译处理中...';
      }
    }
    
    // 更新进度信息
    if (progressInfo) {
      progressInfo.textContent = message;
    }
  }
  
  // 记录到控制台
  console.log(`[网页翻译] ${message}`);
}

// 翻译所有文本块
async function translateChunks(chunks, config) {
  const results = [];
  // 限制并发数为1，避免竞争条件
  const concurrentLimit = 1; // 强制设为1，确保顺序处理
  let completedChunks = 0;
  
  // 并发控制
  const semaphore = new Semaphore(concurrentLimit);
  
  // 创建所有翻译任务
  const tasks = chunks.map((chunk, index) => async () => {
    try {
      // 获取信号量
      await semaphore.acquire();
      
      // 更新进度
      updateLoadingOverlay(`翻译中 (${completedChunks}/${chunks.length})`);
      console.log(`[翻译器] 正在翻译第${index+1}块，共${chunks.length}块`);
      
      // 翻译当前块
      const result = await translateChunk(chunk, config);
      
      // 更新进度
      completedChunks++;
      updateLoadingOverlay(`翻译中 (${completedChunks}/${chunks.length})`);
      
      // 保存结果
      results.push({
        index: index,
        success: true,
        translations: result.translations
      });
      
      // 输出成功信息
      console.log(`[翻译器] 第${index+1}块翻译完成，包含${result.translations.length}个翻译项`);
      
      // 释放信号量
      semaphore.release();
      return result;
    } catch (error) {
      console.error(`[翻译器] 第${index+1}块翻译失败:`, error);
      semaphore.release();
      results.push({
        index: index,
        success: false,
        error: error.message
      });
      throw error;
    }
  });
  
  // 顺序执行所有任务，确保按顺序处理
  for (const task of tasks) {
    try {
      await task();
    } catch (error) {
      console.error('[翻译器] 任务执行出错', error);
      // 继续执行其他任务
    }
  }
  
  // 按原顺序整理结果
  return results.sort((a, b) => a.index - b.index);
}

// 信号量实现，用于控制并发翻译请求数
class Semaphore {
  constructor(max) {
    this.max = max;
    this.count = 0;
    this.queue = [];
  }
  
  async acquire() {
    if (this.count < this.max) {
      this.count++;
      return Promise.resolve();
    }
    
    // 等待释放
    return new Promise(resolve => {
      this.queue.push(resolve);
    });
  }
  
  release() {
    this.count--;
    
    if (this.queue.length > 0) {
      this.count++;
      const next = this.queue.shift();
      next();
    }
  }
}

// 翻译单个文本块
function translateChunk(chunk, config) {
  return new Promise((resolve, reject) => {
    const TIMEOUT = 30000; // 30秒超时
    let timeoutId = null;
    
    // 设置超时
    timeoutId = setTimeout(() => {
      reject(new Error('翻译请求超时'));
    }, TIMEOUT);
    
    chrome.runtime.sendMessage(
      { 
        action: 'translateContent', 
        content: chunk.text,
        sourceLanguage: 'auto',
        targetLanguage: config.defaultTargetLanguage || 'zh-CN',
        // 添加标记用于分割每个文本段落
        delimiters: true
      }, 
      response => {
        // 清除超时
        if (timeoutId) clearTimeout(timeoutId);
        
        if (chrome.runtime.lastError) {
          reject(new Error('通信错误: ' + chrome.runtime.lastError.message));
          return;
        }
        
        if (response && response.success) {
          // 解析翻译结果，分配给各个文本节点
          const translations = parseTranslations(chunk, response.translatedContent);
          console.log(`[翻译器] 解析到${translations.length}条翻译结果`);
          
          // 调试：打印部分翻译结果
          if (translations.length > 0) {
            console.log(`[翻译器] 示例翻译: ${translations[0].original.text} => ${translations[0].translated}`);
          }
          
          resolve({ 
            translations: translations,
            metadata: response.metadata
          });
        } else {
          reject(new Error(response ? response.error : '翻译失败'));
        }
      }
    );
  });
}

// 解析翻译结果，将其分配回对应的文本节点
function parseTranslations(chunk, translatedContent) {
  // 输出原始翻译内容以便调试
  console.log(`[翻译器] 收到翻译内容: ${translatedContent.substring(0, 100)}${translatedContent.length > 100 ? '...' : ''}`);
  
  // 将翻译结果按行分割，对应到原始文本节点
  const translatedLines = translatedContent.split('\n');
  const originalNodes = chunk.nodes;
  
  // 确保我们有足够的翻译行
  const translations = [];
  for (let i = 0; i < originalNodes.length; i++) {
    // 如果对应的翻译行不存在，使用原始文本
    const translatedText = i < translatedLines.length ? translatedLines[i] : originalNodes[i].text;
    
    // 只有翻译和原文不同时才添加
    if (translatedText.trim() !== originalNodes[i].text.trim()) {
      translations.push({
        original: originalNodes[i],
        translated: translatedText
      });
    }
  }
  
  return translations;
}

// 应用翻译结果到页面
function applyTranslationResults(originalNodes, results) {
  // 使用Promise包装配置获取过程
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
      if (!response || !response.success) {
        debugLog('获取配置失败', response);
        reject(new Error('获取配置失败'));
        return;
      }
      
      const config = response.config;
      // 使用配置中的翻译样式
      const translationStyle = config.translationStyle || 'replace';
      const underlineColor = config.underlineColor || '#4285f4';

      debugLog('应用翻译样式', { style: translationStyle, color: underlineColor });
      console.log(`[翻译器] 使用样式: ${translationStyle}`);
      
      const translationMap = new Map();
      // 收集所有虚拟节点的原始节点及其翻译结果
      const virtualNodeTranslations = new Map();
      
      // 从所有结果中提取翻译映射
      results.forEach(result => {
        if (result.success && result.translations) {
          result.translations.forEach(item => {
            const originalNode = item.original;
            // 检查是否是虚拟节点
            if (originalNode.isVirtual && originalNode.originalNode) {
              // 为虚拟节点的原始节点收集翻译
              if (!virtualNodeTranslations.has(originalNode.originalNode)) {
                virtualNodeTranslations.set(originalNode.originalNode, []);
              }
              virtualNodeTranslations.get(originalNode.originalNode).push({
                original: originalNode.text,
                translated: item.translated
              });
            } else {
              // 常规节点直接保存原始文本和翻译
              if (!originalTextMap.has(originalNode.node)) {
                originalTextMap.set(originalNode.node, originalNode.node.textContent);
              }
              translationMap.set(originalNode.node, item.translated);
            }
          });
        }
      });
      
      // 处理虚拟节点的原始节点
      virtualNodeTranslations.forEach((translations, originalNode) => {
        // 保存原始内容
        if (!originalTextMap.has(originalNode)) {
          originalTextMap.set(originalNode, originalNode.textContent);
        }
        
        // 获取原始文本
        const fullText = originalNode.textContent;
        let newText = fullText;
        
        // 应用所有翻译，按原文从长到短排序以避免替换冲突
        translations.sort((a, b) => b.original.length - a.original.length);
        
        translations.forEach(item => {
          const { original, translated } = item;

          // 跳过相同的文本
          if (original.trim() === translated.trim()) {
            return;
          }

          // 根据翻译样式应用不同的显示方式
          let replacementText;
          switch (translationStyle) {
            case 'underline':
              // 下划线+括号原文样式
              replacementText = `<span style="text-decoration: underline; text-decoration-color: ${underlineColor}; cursor: help;" title="原文: ${original}">${translated}</span> (${original})`;
              break;
            case 'tooltip':
              // 鼠标悬停显示原文样式
              replacementText = `<span style="cursor: help; border-bottom: 1px dotted ${underlineColor};" title="原文: ${original}">${translated}</span>`;
              break;
            case 'replace':
            default:
              // 直接替换原文
              replacementText = translated;
              break;
          }

          newText = newText.replace(original, replacementText);
        });
        
        // 设置新内容
        if (newText !== fullText) {
          try {
            // 根据翻译样式选择设置方式
            if (translationStyle === 'replace') {
              // 直接替换时使用textContent
              originalNode.textContent = newText;
            } else {
              // 带样式时使用innerHTML
              originalNode.innerHTML = newText;
            }
            // 添加标记
            originalNode.dataset.translated = 'true';
            console.log(`[翻译器] 成功替换虚拟节点: ${fullText.substring(0, 30)} => ${newText.substring(0, 30)}`);
          } catch (e) {
            console.error(`[翻译器] 替换虚拟节点失败:`, e);
          }
        }
      });
      
      // 应用翻译到常规节点
      let appliedCount = 0;
      originalNodes.forEach(item => {
        // 跳过虚拟节点，因为它们已经被处理
        if (item.isVirtual) {
          return;
        }
        
        const translatedText = translationMap.get(item.node);
        if (translatedText) {
          const originalText = item.node.textContent.trim();
          
          // 如果翻译和原文相同，不做特殊处理
          if (translatedText.trim() === originalText) {
            return;
          }
          
          try {
            // 根据翻译样式应用不同的显示方式
            switch (translationStyle) {
              case 'underline':
                // 下划线+括号原文样式
                const underlineSpan = document.createElement('span');
                underlineSpan.style.textDecoration = 'underline';
                underlineSpan.style.textDecorationColor = underlineColor;
                underlineSpan.style.cursor = 'help';
                underlineSpan.title = `原文: ${originalText}`;
                underlineSpan.textContent = translatedText;

                const originalTextSpan = document.createTextNode(` (${originalText})`);

                // 创建包装容器
                const wrapper = document.createElement('span');
                wrapper.appendChild(underlineSpan);
                wrapper.appendChild(originalTextSpan);

                // 替换原节点
                item.node.parentNode.replaceChild(wrapper, item.node);
                wrapper.dataset.translated = 'true';
                break;

              case 'tooltip':
                // 鼠标悬停显示原文样式
                const tooltipSpan = document.createElement('span');
                tooltipSpan.style.cursor = 'help';
                tooltipSpan.style.borderBottom = `1px dotted ${underlineColor}`;
                tooltipSpan.title = `原文: ${originalText}`;
                tooltipSpan.textContent = translatedText;

                // 替换原节点
                item.node.parentNode.replaceChild(tooltipSpan, item.node);
                tooltipSpan.dataset.translated = 'true';
                break;

              case 'replace':
              default:
                // 直接替换原文
                item.node.textContent = translatedText;
                item.node.dataset.translated = 'true';
                break;
            }

            appliedCount++;
            console.log(`[翻译器] 成功替换节点 (${translationStyle}): ${originalText.substring(0, 30)} => ${translatedText.substring(0, 30)}`);
          } catch (e) {
            console.error(`[翻译器] 替换节点失败:`, e);
          }
        }
      });
      
      // 计算虚拟节点的数量
      const virtualCount = virtualNodeTranslations.size;
      
      debugLog(`应用了${appliedCount}个常规翻译和${virtualCount}个句子级翻译`, { style: translationStyle });
      resolve(appliedCount + virtualCount);
    });
  });
}

// 显示翻译加载中
function showTranslationLoading() {
  debugLog('显示翻译加载中');
  
  // 移除现有覆盖层
  const existingOverlay = document.getElementById('translation-overlay');
  if (existingOverlay) {
    existingOverlay.remove();
  }
  
  // 创建遮罩层显示"翻译中..."
  const overlay = document.createElement('div');
  overlay.id = 'translation-overlay';
  overlay.style.position = 'fixed';
  overlay.style.top = '10px';
  overlay.style.right = '10px';
  overlay.style.backgroundColor = 'rgba(66, 133, 244, 0.9)';
  overlay.style.color = 'white';
  overlay.style.padding = '15px 20px';
  overlay.style.borderRadius = '5px';
  overlay.style.zIndex = '10000';
  overlay.style.fontSize = '14px';
  overlay.style.fontWeight = 'bold';
  overlay.style.boxShadow = '0 2px 10px rgba(0,0,0,0.3)';
  overlay.style.minWidth = '200px';
  overlay.style.textAlign = 'center';
  
  // 添加加载指示器
  const loadingIndicator = document.createElement('div');
  loadingIndicator.style.display = 'inline-block';
  loadingIndicator.style.width = '20px';
  loadingIndicator.style.height = '20px';
  loadingIndicator.style.marginRight = '10px';
  loadingIndicator.style.border = '3px solid rgba(255,255,255,0.3)';
  loadingIndicator.style.borderRadius = '50%';
  loadingIndicator.style.borderTop = '3px solid white';
  loadingIndicator.style.animation = 'translator-spin 1s linear infinite';
  
  // 添加动画样式
  const style = document.createElement('style');
  style.textContent = `
    @keyframes translator-spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
  
  const textSpan = document.createElement('span');
  textSpan.textContent = '翻译中...';
  
  // 添加进度信息
  const progressInfo = document.createElement('div');
  progressInfo.id = 'translation-progress';
  progressInfo.style.fontSize = '12px';
  progressInfo.style.marginTop = '10px';
  progressInfo.textContent = '正在准备翻译...';
  
  overlay.appendChild(loadingIndicator);
  overlay.appendChild(textSpan);
  overlay.appendChild(progressInfo);
  document.body.appendChild(overlay);
  
  // 打印到控制台
  console.log('%c[网页翻译] 开始翻译过程', 'font-weight: bold; color: #4285f4; font-size: 14px;');
}

// 恢复原始内容
function restoreOriginal() {
  debugLog('恢复原始内容');
  
  if (originalTextMap.size > 0) {
    // 恢复所有被修改的文本节点
    originalTextMap.forEach((originalText, node) => {
      try {
        // 检查节点是否仍在DOM中
        if (!document.contains(node)) {
          return;
        }
        
        // 直接恢复原始文本，不再区分节点类型
        node.textContent = originalText;
        
        // 移除翻译标记
        if (node.dataset) {
          delete node.dataset.translated;
          delete node.dataset.highlighted;
        }
      } catch (e) {
        console.error('恢复节点内容时出错', e);
      }
    });
    
    // 清空映射
    originalTextMap.clear();
    
    // 移除状态指示器
    const statusIndicator = document.getElementById('translation-status');
    if (statusIndicator) {
      statusIndicator.remove();
    }
    
    isTranslated = false;
    console.log('[翻译器] 已恢复所有原始内容');
  } else if (originalContent) {
    // 作为备份，如果没有文本映射，则恢复整个body内容
    // 这种情况不应该发生，但作为安全措施保留
    document.body.innerHTML = originalContent;
    isTranslated = false;
    console.log('[翻译器] 使用备份方法恢复原始内容');
  }
}

// 显示翻译状态指示器，不替换页面内容
function showTranslationStatus(message) {
  debugLog('显示翻译状态', message);
  
  // 移除加载提示
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.remove();
  }
  
  // 移除现有状态指示器
  const existingIndicator = document.getElementById('translation-status');
  if (existingIndicator) {
    existingIndicator.remove();
  }
  
  // 添加翻译状态指示
  const statusIndicator = document.createElement('div');
  statusIndicator.id = 'translation-status';
  statusIndicator.style.position = 'fixed';
  statusIndicator.style.top = '10px';
  statusIndicator.style.right = '10px';
  statusIndicator.style.backgroundColor = 'rgba(0, 150, 0, 0.9)';
  statusIndicator.style.color = 'white';
  statusIndicator.style.padding = '15px 20px';
  statusIndicator.style.borderRadius = '5px';
  statusIndicator.style.zIndex = '10000';
  statusIndicator.style.fontSize = '14px';
  statusIndicator.style.fontFamily = 'Arial, sans-serif';
  statusIndicator.style.boxShadow = '0 2px 10px rgba(0,0,0,0.3)';
  statusIndicator.style.cursor = 'pointer';
  statusIndicator.style.display = 'flex';
  statusIndicator.style.flexDirection = 'column';
  statusIndicator.style.alignItems = 'center';
  statusIndicator.style.justifyContent = 'center';
  statusIndicator.style.gap = '10px';
  statusIndicator.style.minWidth = '200px';
  
  // 添加状态文本
  const statusText = document.createElement('span');
  statusText.textContent = message;
  statusText.style.fontWeight = 'bold';
  statusIndicator.appendChild(statusText);
  
  // 添加统计信息文本
  const statsText = document.createElement('span');
  statsText.id = 'translation-stats';
  statsText.style.fontSize = '12px';
  statsText.textContent = '正在收集翻译统计...';
  statusIndicator.appendChild(statsText);
  
  // 添加恢复按钮
  const restoreButton = document.createElement('button');
  restoreButton.textContent = '恢复原文';
  restoreButton.style.backgroundColor = 'white';
  restoreButton.style.color = '#4285f4';
  restoreButton.style.border = 'none';
  restoreButton.style.padding = '8px 12px';
  restoreButton.style.borderRadius = '3px';
  restoreButton.style.cursor = 'pointer';
  restoreButton.style.fontSize = '13px';
  restoreButton.style.fontWeight = 'bold';
  restoreButton.style.marginTop = '5px';
  
  restoreButton.onclick = () => {
    debugLog('点击恢复原文按钮');
    restoreOriginal();
  };
  
  // 添加查看翻译内容按钮
  const highlightButton = document.createElement('button');
  highlightButton.textContent = '突出显示翻译内容';
  highlightButton.style.backgroundColor = '#ffeb3b';
  highlightButton.style.color = '#333';
  highlightButton.style.border = 'none';
  highlightButton.style.padding = '8px 12px';
  highlightButton.style.borderRadius = '3px';
  highlightButton.style.cursor = 'pointer';
  highlightButton.style.fontSize = '13px';
  
  highlightButton.onclick = () => {
    debugLog('突出显示翻译内容');
    highlightTranslatedContent();
  };
  
  statusIndicator.appendChild(restoreButton);
  statusIndicator.appendChild(highlightButton);
  
  document.body.appendChild(statusIndicator);
  
  // 收集并显示翻译统计信息
  setTimeout(updateTranslationStats, 500);
}

// 突出显示已翻译内容
function highlightTranslatedContent() {
  // 寻找所有带有translated标记的元素
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  
  if (translatedElements.length === 0) {
    alert('未找到已翻译的内容，请尝试先进行翻译');
    return;
  }
  
  // 为每个翻译元素添加高亮效果
  translatedElements.forEach(el => {
    // 检查是否已有高亮
    if (el.dataset.highlighted === 'true') {
      // 移除高亮
      el.style.backgroundColor = '';
      el.style.outline = '';
      delete el.dataset.highlighted;
    } else {
      // 添加高亮
      el.style.backgroundColor = 'rgba(255, 235, 59, 0.3)';
      el.style.outline = '2px solid #ffc107';
      el.dataset.highlighted = 'true';
      
      // 确保元素可见（滚动到视图中）
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
}

// 更新翻译统计信息
function updateTranslationStats() {
  const statsElement = document.getElementById('translation-stats');
  if (!statsElement) return;
  
  // 计算已翻译的元素数量
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  const translatedCount = translatedElements.length;
  
  // 查找带有样式的翻译元素数量
  const styledElements = document.querySelectorAll('span[style*="text-decoration: underline"]').length;
  
  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) return;
    
    const config = response.config;
    statsElement.textContent = `已翻译${translatedCount}个元素 • 样式: ${config.translationStyle || 'replace'} • 比例: ${config.translationPercentage || 100}%`;
    
    // 在控制台中打印详细信息
    console.log('%c[网页翻译] 翻译统计', 'font-weight: bold; color: #4285f4;');
    console.log(`总共翻译了 ${translatedCount} 个内容块`);
    console.log(`翻译样式: ${config.translationStyle || 'replace'}`);
    console.log(`翻译比例: ${config.translationPercentage || 100}%`);
    console.log(`下划线样式元素: ${styledElements}`);
    
    // 打印前5个已翻译元素的内容
    console.log('%c已翻译内容示例:', 'font-weight: bold;');
    translatedElements.forEach((el, index) => {
      if (index < 5) {
        console.log(`${index + 1}. ${el.textContent.substring(0, 100)}${el.textContent.length > 100 ? '...' : ''}`);
      }
    });
  });
}

// 显示翻译错误
function showTranslationError(errorMessage) {
  debugLog('显示翻译错误', errorMessage);
  // 移除加载提示
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.remove();
  }
  
  // 显示错误消息
  const errorIndicator = document.createElement('div');
  errorIndicator.id = 'translation-error';
  errorIndicator.style.position = 'fixed';
  errorIndicator.style.top = '10px';
  errorIndicator.style.right = '10px';
  errorIndicator.style.backgroundColor = 'rgba(200, 0, 0, 0.7)';
  errorIndicator.style.color = 'white';
  errorIndicator.style.padding = '10px 20px';
  errorIndicator.style.borderRadius = '5px';
  errorIndicator.style.zIndex = '10000';
  errorIndicator.textContent = `翻译错误: ${errorMessage}`;
  
  document.body.appendChild(errorIndicator);
  
  // 3秒后自动消失
  setTimeout(() => {
    errorIndicator.remove();
  }, 3000);
} 