// 插件初始化
let isTranslated = false;
let originalContent = null;
let translatedContent = null;
// 存储原始节点内容的映射，用于恢复
let originalTextMap = new Map();

// 简化的调试日志函数 - 仅在开发模式下输出
function debugLog(message, data = null) {
  // 仅在开发模式下输出日志
  if (false) { // 设为 false 禁用调试日志
    console.log(`[Translator] ${message}`, data || '');
  }
}

// 页面加载完成后检查自动翻译
document.addEventListener('DOMContentLoaded', () => {
  checkAutoTranslate();
});

// 如果页面已经加载完成，立即检查
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkAutoTranslate);
} else {
  checkAutoTranslate();
}

// 检查是否需要自动翻译
function checkAutoTranslate() {
  // 获取配置并检查自动翻译设置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (response && response.success && response.config.enableAutoTranslate) {
      debugLog('自动翻译已启用，开始翻译页面');
      // 延迟一秒后开始翻译，确保页面完全加载
      setTimeout(() => {
        translatePage();
      }, 1000);
    }
  });
}

// 手动测试翻译功能的按钮 - 已禁用
function addTestButton() {
  // 禁用测试按钮
  return;
  
  /*
  const testButton = document.createElement('button');
  testButton.textContent = '测试翻译功能';
  testButton.style.position = 'fixed';
  testButton.style.top = '50px';
  testButton.style.right = '10px';
  testButton.style.zIndex = '10001';
  testButton.style.padding = '5px 10px';
  testButton.style.backgroundColor = '#4285f4';
  testButton.style.color = 'white';
  testButton.style.border = 'none';
  testButton.style.borderRadius = '4px';
  testButton.style.cursor = 'pointer';
  
  testButton.onclick = () => {
    debugLog('手动触发翻译');
    translatePage();
  };
  
  document.body.appendChild(testButton);
  debugLog('添加了测试按钮');
  */
}

// 不再添加测试按钮
// setTimeout(addTestButton, 1000);

// 监听来自popup或background的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  debugLog('收到消息', message);
  
  if (message.action === 'ping') {
    // 响应ping消息，确认内容脚本已加载
    debugLog('收到ping消息，确认内容脚本已加载');
    sendResponse({ success: true, status: 'content_script_ready' });
  } else if (message.action === 'translate') {
    debugLog('执行翻译操作');
    if (!isTranslated) {
      translatePage();
    } else {
      restoreOriginal();
    }
    sendResponse({ success: true, isTranslated: isTranslated });
  } else if (message.action === 'getStatus') {
    debugLog('获取状态', { isTranslated });
    sendResponse({ isTranslated: isTranslated });
  }
  return true;
});

// 翻译页面
function translatePage() {
  debugLog('开始翻译页面');
  // 保存原始内容
  if (!originalContent) {
    originalContent = document.body.innerHTML;
    debugLog('已保存原始内容');
  }

  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) {
      debugLog('获取配置失败', response);
      showTranslationError('获取配置失败');
      return;
    }

    const config = response.config;
    debugLog('获取配置成功', config);
    
    // 显示加载中提示
    showTranslationLoading();
    
    // 提取页面文本节点
    const textNodes = extractTextNodes(document.body);
    debugLog(`提取到${textNodes.length}个文本节点`);

    // 按比例选择需要翻译的节点
    const nodesToTranslate = selectNodesToTranslate(textNodes, config);
    debugLog(`将翻译${nodesToTranslate.length}个文本节点（${config.translationPercentage}%）`);
    
    if (nodesToTranslate.length === 0) {
      showTranslationError('没有找到可翻译的文本');
      return;
    }
    
    // 分块处理文本
    const textChunks = splitIntoChunks(nodesToTranslate, config.maxChunkSize);
    debugLog(`文本已分为${textChunks.length}个块进行处理`);
    console.log(`[网页翻译] 文本已分为${textChunks.length}个块进行处理`);
    
    // 显示进度信息
    updateLoadingOverlay(`准备翻译 (0/${textChunks.length})`);
    
    // 使用Promise处理所有翻译任务
    translateChunks(textChunks, config)
      .then(results => {
        // 应用翻译结果到页面
        applyTranslationResults(nodesToTranslate, results)
          .then(appliedCount => {
            debugLog(`翻译完成! 成功应用了${appliedCount}个翻译`);

            isTranslated = true;
            // 不再替换整个页面，而是显示翻译状态指示
            showTranslationStatus("部分内容已翻译");
          })
          .catch(error => {
            debugLog('翻译过程出错', error);
            showTranslationError(error.message || '翻译失败');
          });
      })
      .catch(error => {
        debugLog('翻译过程出错', error);
        showTranslationError(error.message || '翻译失败');
      });
  });
}

// 提取页面中的所有文本节点
function extractTextNodes(node) {
  const textNodes = [];
  
  // 递归函数来遍历DOM树
  function traverse(node) {
    // 如果是文本节点且包含非空文本
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim();
      if (text.length > 0) {
        // 对于长文本，尝试按短语分割
        if (text.length > 100) {
          splitTextIntoPhrases(node, textNodes);
        } else {
          textNodes.push({
            node: node,
            text: text,
            length: text.length,
            parentTag: node.parentNode.tagName,
            // 基本权重：标题 > 段落 > 其他
            importance: getNodeImportance(node)
          });
        }
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 跳过脚本、样式和隐藏元素
      const style = window.getComputedStyle(node);
      if (node.tagName !== 'SCRIPT' && 
          node.tagName !== 'STYLE' && 
          style.display !== 'none' && 
          style.visibility !== 'hidden') {
        // 遍历子节点
        for (let i = 0; i < node.childNodes.length; i++) {
          traverse(node.childNodes[i]);
        }
      }
    }
  }
  
  // 开始遍历
  traverse(node);
  return textNodes;
}

// 将文本节点按单词分割 - 真正的单词级别翻译
function splitTextIntoPhrases(node, textNodes) {
  const text = node.textContent;

  // 如果文本太短，直接使用整个节点
  if (text.length < 10) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
    return;
  }

  // 创建一个临时容器来存放分割的单词
  const tempContainer = document.createElement('div');
  tempContainer.style.display = 'none';
  document.body.appendChild(tempContainer);

  // 分割文本为单词 - 支持英文单词和中文字符
  // 匹配英文单词（包括连字符）、数字、中文字符，但排除纯标点符号
  const wordPattern = /[\w\-']+|[\u4e00-\u9fff]/g;
  const words = text.match(wordPattern);

  // 如果无法分割成单词，使用整个节点
  if (!words || words.length === 0) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
  } else {
    // 为每个单词创建独立的虚拟节点
    let currentPosition = 0;
    words.forEach((word, index) => {
      // 找到单词在原文中的位置
      const wordStart = text.indexOf(word, currentPosition);
      const wordEnd = wordStart + word.length;

      // 获取单词前的空格和标点
      const beforeWord = text.substring(currentPosition, wordStart);

      // 创建单词节点
      const wordNode = document.createTextNode(word);
      tempContainer.appendChild(wordNode);

      textNodes.push({
        node: wordNode,
        text: word,
        length: word.length,
        parentTag: node.parentNode.tagName,
        importance: getNodeImportance(node),
        isVirtual: true,
        originalNode: node,
        wordIndex: index,
        beforeText: beforeWord,
        position: { start: wordStart, end: wordEnd }
      });

      currentPosition = wordEnd;
    });
  }

  // 移除临时容器
  document.body.removeChild(tempContainer);

  // 如果没有添加任何节点（分割失败），则使用原始节点
  if (!textNodes.some(item => item.originalNode === node)) {
    textNodes.push({
      node: node,
      text: text,
      length: text.length,
      parentTag: node.parentNode.tagName,
      importance: getNodeImportance(node)
    });
  }
}

// 获取节点的重要性分数
function getNodeImportance(node) {
  const parent = node.parentNode;
  if (!parent) return 1;
  
  const tagName = parent.tagName;
  
  // 根据标签类型分配权重
  switch(tagName) {
    case 'H1': return 10;
    case 'H2': return 9;
    case 'H3': return 8;
    case 'H4': return 7;
    case 'H5': return 6;
    case 'H6': return 5;
    case 'P': return 4;
    case 'LI': return 3;
    case 'DIV': return 2;
    default: return 1;
  }
}

// 根据翻译比例选择需要翻译的节点 - 分布式选择算法
function selectNodesToTranslate(textNodes, config) {
  if (!textNodes.length) return [];

  const percentage = config.translationPercentage / 100;
  const mode = config.translationMode || 'distributed';

  // 如果是100%，直接返回所有节点
  if (percentage >= 1) {
    return textNodes;
  }

  // 计算需要翻译的节点数量
  const targetCount = Math.max(1, Math.floor(textNodes.length * percentage));

  switch(mode) {
    case 'distributed':
      // 分布式选择 - 确保翻译的单词均匀分布在整个内容中
      return selectDistributedNodes(textNodes, targetCount);

    case 'random':
      // 随机选择节点
      return shuffleArray(textNodes).slice(0, targetCount);

    case 'top':
      // 从顶部开始选择
      return textNodes.slice(0, targetCount);

    case 'important':
      // 按重要性排序后选择
      return textNodes
        .sort((a, b) => b.importance - a.importance)
        .slice(0, targetCount);

    default:
      return selectDistributedNodes(textNodes, targetCount);
  }
}

// 分布式选择算法 - 确保翻译内容均匀分布
function selectDistributedNodes(textNodes, targetCount) {
  if (targetCount >= textNodes.length) {
    return textNodes;
  }

  // 按照在页面中的位置对节点进行分组
  const groups = groupNodesByPosition(textNodes);

  // 计算每个组应该选择的节点数量
  const selectedNodes = [];
  const totalGroups = groups.length;

  groups.forEach((group, groupIndex) => {
    // 计算这个组应该选择的节点数量（均匀分布）
    const groupTargetCount = Math.round((targetCount * group.length) / textNodes.length);
    const actualCount = Math.min(groupTargetCount, group.length);

    if (actualCount > 0) {
      // 在组内均匀选择节点
      const step = Math.max(1, Math.floor(group.length / actualCount));
      for (let i = 0; i < actualCount; i++) {
        const nodeIndex = Math.min(i * step, group.length - 1);
        selectedNodes.push(group[nodeIndex]);
      }
    }
  });

  // 如果选择的节点数量不够，从剩余节点中补充
  if (selectedNodes.length < targetCount) {
    const remainingNodes = textNodes.filter(node => !selectedNodes.includes(node));
    const additionalCount = targetCount - selectedNodes.length;
    const additionalNodes = shuffleArray(remainingNodes).slice(0, additionalCount);
    selectedNodes.push(...additionalNodes);
  }

  // 如果选择的节点数量过多，随机移除一些
  if (selectedNodes.length > targetCount) {
    return shuffleArray(selectedNodes).slice(0, targetCount);
  }

  return selectedNodes;
}

// 按位置对节点进行分组
function groupNodesByPosition(textNodes) {
  // 将节点按照在DOM中的位置分成若干组
  const groupSize = Math.max(10, Math.floor(textNodes.length / 10)); // 每组至少10个节点
  const groups = [];

  for (let i = 0; i < textNodes.length; i += groupSize) {
    const group = textNodes.slice(i, i + groupSize);
    if (group.length > 0) {
      groups.push(group);
    }
  }

  return groups;
}

// 辅助函数：随机打乱数组
function shuffleArray(array) {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

// 将文本节点分成块，以便批量翻译 - 优化的智能分块算法
function splitIntoChunks(textNodes, maxChunkSize) {
  const chunks = [];
  let currentChunk = [];
  let currentSize = 0;

  // 动态调整块大小，确保更好的并发性能
  const optimalChunkSize = Math.min(maxChunkSize, Math.max(500, maxChunkSize / 2));
  const minChunkSize = Math.min(200, optimalChunkSize / 2);

  debugLog(`分块参数: 最大=${maxChunkSize}, 最优=${optimalChunkSize}, 最小=${minChunkSize}`);

  for (let i = 0; i < textNodes.length; i++) {
    const item = textNodes[i];
    const itemSize = item.text.length;

    // 如果单个项目就超过最大大小，单独成块
    if (itemSize > maxChunkSize) {
      // 先保存当前块（如果有内容）
      if (currentChunk.length > 0) {
        chunks.push(createChunk(currentChunk, currentSize));
        currentChunk = [];
        currentSize = 0;
      }

      // 大项目单独成块
      chunks.push(createChunk([item], itemSize));
      continue;
    }

    // 检查是否需要开始新块
    const wouldExceedOptimal = currentSize + itemSize > optimalChunkSize;
    const wouldExceedMax = currentSize + itemSize > maxChunkSize;
    const hasMinimumContent = currentSize >= minChunkSize;

    if (currentSize > 0 && (wouldExceedMax || (wouldExceedOptimal && hasMinimumContent))) {
      // 保存当前块并开始新块
      chunks.push(createChunk(currentChunk, currentSize));
      currentChunk = [];
      currentSize = 0;
    }

    // 添加到当前块
    currentChunk.push(item);
    currentSize += itemSize;
  }

  // 添加最后一个块
  if (currentChunk.length > 0) {
    chunks.push(createChunk(currentChunk, currentSize));
  }

  // 优化小块：合并过小的块
  const optimizedChunks = mergeSmallChunks(chunks, minChunkSize, maxChunkSize);

  debugLog(`分块完成: 原始${chunks.length}块 -> 优化后${optimizedChunks.length}块`);

  return optimizedChunks;
}

// 创建文本块
function createChunk(nodes, size) {
  return {
    nodes: nodes,
    text: nodes.map(n => n.text).join('\n'),
    size: size,
    wordCount: nodes.reduce((count, n) => count + (n.text.match(/\w+/g) || []).length, 0)
  };
}

// 合并过小的块以提高效率
function mergeSmallChunks(chunks, minSize, maxSize) {
  if (chunks.length <= 1) return chunks;

  const optimized = [];
  let i = 0;

  while (i < chunks.length) {
    let currentChunk = chunks[i];

    // 如果当前块太小，尝试与下一个块合并
    while (i + 1 < chunks.length &&
           currentChunk.size < minSize &&
           currentChunk.size + chunks[i + 1].size <= maxSize) {
      const nextChunk = chunks[i + 1];

      // 合并块
      currentChunk = {
        nodes: [...currentChunk.nodes, ...nextChunk.nodes],
        text: currentChunk.text + '\n' + nextChunk.text,
        size: currentChunk.size + nextChunk.size,
        wordCount: currentChunk.wordCount + nextChunk.wordCount
      };

      i++; // 跳过已合并的块
    }

    optimized.push(currentChunk);
    i++;
  }

  return optimized;
}

// 更新加载覆盖层的内容
function updateLoadingOverlay(message, details = null) {
  const overlay = document.getElementById('translation-overlay');
  const progressInfo = document.getElementById('translation-progress');
  const detailInfo = document.getElementById('translation-details');

  if (overlay) {
    // 更新进度信息
    if (progressInfo) {
      progressInfo.textContent = message;
    }

    // 更新详细信息
    if (detailInfo && details) {
      detailInfo.textContent = details;
    }
  }

  debugLog(`更新进度: ${message}${details ? ` - ${details}` : ''}`);
}

// 翻译所有文本块 - 优化的并发处理
async function translateChunks(chunks, config) {
  const results = new Array(chunks.length); // 预分配结果数组
  const concurrentLimit = Math.min(config.concurrentRequests || 3, chunks.length); // 使用配置的并发数
  let completedChunks = 0;

  debugLog(`开始并发翻译，并发数: ${concurrentLimit}，总块数: ${chunks.length}`);

  // 创建进度更新函数（节流）
  let lastProgressUpdate = 0;
  const updateProgress = () => {
    const now = Date.now();
    if (now - lastProgressUpdate > 200) { // 最多每200ms更新一次
      updateLoadingOverlay(`翻译中 (${completedChunks}/${chunks.length})`);
      lastProgressUpdate = now;
    }
  };

  // 创建翻译任务
  const translateTask = async (chunk, index) => {
    const startTime = Date.now();
    try {
      debugLog(`开始翻译块 ${index + 1}/${chunks.length}`);

      const result = await translateChunk(chunk, config);

      // 原子操作更新结果
      results[index] = {
        index: index,
        success: true,
        translations: result.translations,
        duration: Date.now() - startTime
      };

      // 原子操作更新计数器
      completedChunks++;
      updateProgress();

      debugLog(`块 ${index + 1} 翻译完成，耗时 ${Date.now() - startTime}ms，包含 ${result.translations.length} 个翻译项`);

      return result;
    } catch (error) {
      debugLog(`块 ${index + 1} 翻译失败`, error);

      results[index] = {
        index: index,
        success: false,
        error: error.message,
        duration: Date.now() - startTime
      };

      completedChunks++;
      updateProgress();

      // 不抛出错误，让其他任务继续执行
      return null;
    }
  };

  // 使用 Promise.allSettled 进行真正的并发处理
  if (concurrentLimit >= chunks.length) {
    // 如果并发数大于等于块数，直接并发所有任务
    debugLog('使用全并发模式');
    const promises = chunks.map((chunk, index) => translateTask(chunk, index));
    await Promise.allSettled(promises);
  } else {
    // 使用批次并发处理
    debugLog(`使用批次并发模式，每批 ${concurrentLimit} 个任务`);

    for (let i = 0; i < chunks.length; i += concurrentLimit) {
      const batch = chunks.slice(i, i + concurrentLimit);
      const batchPromises = batch.map((chunk, batchIndex) =>
        translateTask(chunk, i + batchIndex)
      );

      debugLog(`执行批次 ${Math.floor(i / concurrentLimit) + 1}，包含 ${batch.length} 个任务`);
      await Promise.allSettled(batchPromises);
    }
  }

  // 统计结果
  const successCount = results.filter(r => r && r.success).length;
  const failureCount = results.filter(r => r && !r.success).length;
  const totalDuration = results.reduce((sum, r) => sum + (r?.duration || 0), 0);
  const avgDuration = totalDuration / results.length;

  debugLog('并发翻译完成', {
    total: chunks.length,
    success: successCount,
    failure: failureCount,
    totalDuration: `${totalDuration}ms`,
    avgDuration: `${avgDuration.toFixed(2)}ms`,
    concurrency: concurrentLimit
  });

  return results.filter(r => r !== null); // 过滤掉null结果
}

// 移除了Semaphore类，使用Promise.allSettled进行更高效的并发控制

// 翻译单个文本块 - 优化版本，支持重试和更好的错误处理
async function translateChunk(chunk, config, retryCount = 0) {
  const maxRetries = 2;
  const baseTimeout = 30000; // 基础超时30秒
  const timeout = baseTimeout + (retryCount * 10000); // 重试时增加超时时间

  return new Promise((resolve, reject) => {
    let timeoutId = null;
    let isResolved = false;

    // 设置超时
    timeoutId = setTimeout(() => {
      if (!isResolved) {
        isResolved = true;
        const errorMsg = `翻译请求超时 (${timeout/1000}秒)${retryCount > 0 ? ` - 重试 ${retryCount}/${maxRetries}` : ''}`;

        if (retryCount < maxRetries) {
          debugLog(`${errorMsg}，准备重试...`);
          // 递归重试
          translateChunk(chunk, config, retryCount + 1)
            .then(resolve)
            .catch(reject);
        } else {
          reject(new Error(errorMsg));
        }
      }
    }, timeout);

    const startTime = Date.now();

    chrome.runtime.sendMessage(
      {
        action: 'translateContent',
        content: chunk.text,
        sourceLanguage: 'auto',
        targetLanguage: config.defaultTargetLanguage || 'zh-CN',
        delimiters: true,
        chunkInfo: {
          size: chunk.size,
          wordCount: chunk.wordCount,
          nodeCount: chunk.nodes.length
        }
      },
      response => {
        // 清除超时
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }

        if (isResolved) return; // 已经处理过了
        isResolved = true;

        const duration = Date.now() - startTime;

        if (chrome.runtime.lastError) {
          const errorMsg = `通信错误: ${chrome.runtime.lastError.message}`;

          if (retryCount < maxRetries) {
            debugLog(`${errorMsg}，准备重试 ${retryCount + 1}/${maxRetries}...`);
            // 延迟重试
            setTimeout(() => {
              translateChunk(chunk, config, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 1000 * (retryCount + 1)); // 递增延迟
          } else {
            reject(new Error(errorMsg));
          }
          return;
        }

        if (response && response.success) {
          try {
            // 解析翻译结果，分配给各个文本节点
            const translations = parseTranslations(chunk, response.translatedContent);
            debugLog(`块翻译成功: ${translations.length}条结果，耗时${duration}ms`);

            resolve({
              translations: translations,
              metadata: {
                ...response.metadata,
                duration: duration,
                retryCount: retryCount,
                chunkSize: chunk.size,
                wordCount: chunk.wordCount
              }
            });
          } catch (parseError) {
            debugLog('解析翻译结果失败', parseError);

            if (retryCount < maxRetries) {
              debugLog(`解析失败，准备重试 ${retryCount + 1}/${maxRetries}...`);
              setTimeout(() => {
                translateChunk(chunk, config, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              }, 1000 * (retryCount + 1));
            } else {
              reject(new Error(`解析翻译结果失败: ${parseError.message}`));
            }
          }
        } else {
          const errorMsg = response ? response.error : '翻译失败';

          if (retryCount < maxRetries && !errorMsg.includes('API密钥')) {
            debugLog(`翻译失败: ${errorMsg}，准备重试 ${retryCount + 1}/${maxRetries}...`);
            setTimeout(() => {
              translateChunk(chunk, config, retryCount + 1)
                .then(resolve)
                .catch(reject);
            }, 1000 * (retryCount + 1));
          } else {
            reject(new Error(errorMsg));
          }
        }
      }
    );
  });
}

// 解析翻译结果，将其分配回对应的文本节点
function parseTranslations(chunk, translatedContent) {
  debugLog(`收到翻译内容: ${translatedContent.substring(0, 100)}${translatedContent.length > 100 ? '...' : ''}`);

  // 将翻译结果按行分割，对应到原始文本节点
  const translatedLines = translatedContent.split('\n');
  const originalNodes = chunk.nodes;
  
  // 确保我们有足够的翻译行
  const translations = [];
  for (let i = 0; i < originalNodes.length; i++) {
    // 如果对应的翻译行不存在，使用原始文本
    const translatedText = i < translatedLines.length ? translatedLines[i] : originalNodes[i].text;
    
    // 只有翻译和原文不同时才添加
    if (translatedText.trim() !== originalNodes[i].text.trim()) {
      translations.push({
        original: originalNodes[i],
        translated: translatedText
      });
    }
  }
  
  return translations;
}

// 应用翻译结果到页面
function applyTranslationResults(originalNodes, results) {
  // 使用Promise包装配置获取过程
  return new Promise((resolve, reject) => {
    chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
      if (!response || !response.success) {
        debugLog('获取配置失败', response);
        reject(new Error('获取配置失败'));
        return;
      }
      
      const config = response.config;
      // 使用配置中的翻译样式
      const translationStyle = config.translationStyle || 'replace';
      const underlineColor = config.underlineColor || '#4285f4';

      debugLog('应用翻译样式', { style: translationStyle, color: underlineColor });
      
      const translationMap = new Map();
      // 收集所有虚拟节点的原始节点及其翻译结果
      const virtualNodeTranslations = new Map();
      
      // 从所有结果中提取翻译映射
      results.forEach(result => {
        if (result.success && result.translations) {
          result.translations.forEach(item => {
            const originalNode = item.original;
            // 检查是否是虚拟节点
            if (originalNode.isVirtual && originalNode.originalNode) {
              // 为虚拟节点的原始节点收集翻译
              if (!virtualNodeTranslations.has(originalNode.originalNode)) {
                virtualNodeTranslations.set(originalNode.originalNode, []);
              }
              virtualNodeTranslations.get(originalNode.originalNode).push({
                original: originalNode.text,
                translated: item.translated
              });
            } else {
              // 常规节点直接保存原始文本和翻译
              if (!originalTextMap.has(originalNode.node)) {
                originalTextMap.set(originalNode.node, originalNode.node.textContent);
              }
              translationMap.set(originalNode.node, item.translated);
            }
          });
        }
      });
      
      // 处理虚拟节点的原始节点
      virtualNodeTranslations.forEach((translations, originalNode) => {
        // 保存原始内容
        if (!originalTextMap.has(originalNode)) {
          originalTextMap.set(originalNode, originalNode.textContent);
        }
        
        // 获取原始文本
        const fullText = originalNode.textContent;
        let newText = fullText;
        
        // 应用所有翻译，按原文从长到短排序以避免替换冲突
        translations.sort((a, b) => b.original.length - a.original.length);
        
        translations.forEach(item => {
          const { original, translated } = item;

          // 跳过相同的文本
          if (original.trim() === translated.trim()) {
            return;
          }

          // 根据翻译样式应用不同的显示方式
          let replacementText;
          switch (translationStyle) {
            case 'underline':
              // 下划线+括号原文样式
              replacementText = `<span class="translated-word" data-original="${original}" data-translated="${translated}" style="text-decoration: underline; text-decoration-color: ${underlineColor}; cursor: help;" title="原文: ${original}">${translated}</span> (${original})`;
              break;
            case 'tooltip':
              // 鼠标悬停显示原文样式
              replacementText = `<span class="translated-word" data-original="${original}" data-translated="${translated}" style="cursor: help; border-bottom: 1px dotted ${underlineColor};" title="原文: ${original}">${translated}</span>`;
              break;
            case 'replace':
            default:
              // 直接替换原文
              replacementText = translated;
              break;
          }

          newText = newText.replace(original, replacementText);
        });
        
        // 设置新内容
        if (newText !== fullText) {
          try {
            // 根据翻译样式选择设置方式
            if (translationStyle === 'replace') {
              // 直接替换时使用textContent
              originalNode.textContent = newText;
            } else {
              // 带样式时使用innerHTML
              originalNode.innerHTML = newText;
            }
            // 添加标记
            originalNode.dataset.translated = 'true';
            debugLog(`成功替换虚拟节点: ${fullText.substring(0, 30)} => ${newText.substring(0, 30)}`);
          } catch (e) {
            debugLog(`替换虚拟节点失败`, e);
          }
        }
      });
      
      // 应用翻译到常规节点
      let appliedCount = 0;
      originalNodes.forEach(item => {
        // 跳过虚拟节点，因为它们已经被处理
        if (item.isVirtual) {
          return;
        }
        
        const translatedText = translationMap.get(item.node);
        if (translatedText) {
          const originalText = item.node.textContent.trim();
          
          // 如果翻译和原文相同，不做特殊处理
          if (translatedText.trim() === originalText) {
            return;
          }
          
          try {
            // 根据翻译样式应用不同的显示方式
            switch (translationStyle) {
              case 'underline':
                // 下划线+括号原文样式
                const underlineSpan = document.createElement('span');
                underlineSpan.className = 'translated-word';
                underlineSpan.setAttribute('data-original', originalText);
                underlineSpan.setAttribute('data-translated', translatedText);
                underlineSpan.style.textDecoration = 'underline';
                underlineSpan.style.textDecorationColor = underlineColor;
                underlineSpan.style.cursor = 'help';
                underlineSpan.title = `原文: ${originalText}`;
                underlineSpan.textContent = translatedText;

                const originalTextSpan = document.createTextNode(` (${originalText})`);

                // 创建包装容器
                const wrapper = document.createElement('span');
                wrapper.appendChild(underlineSpan);
                wrapper.appendChild(originalTextSpan);

                // 替换原节点
                item.node.parentNode.replaceChild(wrapper, item.node);
                wrapper.dataset.translated = 'true';
                break;

              case 'tooltip':
                // 鼠标悬停显示原文样式
                const tooltipSpan = document.createElement('span');
                tooltipSpan.className = 'translated-word';
                tooltipSpan.setAttribute('data-original', originalText);
                tooltipSpan.setAttribute('data-translated', translatedText);
                tooltipSpan.style.cursor = 'help';
                tooltipSpan.style.borderBottom = `1px dotted ${underlineColor}`;
                tooltipSpan.title = `原文: ${originalText}`;
                tooltipSpan.textContent = translatedText;

                // 替换原节点
                item.node.parentNode.replaceChild(tooltipSpan, item.node);
                tooltipSpan.dataset.translated = 'true';
                break;

              case 'replace':
              default:
                // 直接替换原文
                item.node.textContent = translatedText;
                item.node.dataset.translated = 'true';
                break;
            }

            appliedCount++;
            debugLog(`成功替换节点 (${translationStyle}): ${originalText.substring(0, 30)} => ${translatedText.substring(0, 30)}`);
          } catch (e) {
            debugLog(`替换节点失败`, e);
          }
        }
      });
      
      // 计算虚拟节点的数量
      const virtualCount = virtualNodeTranslations.size;
      
      debugLog(`应用了${appliedCount}个常规翻译和${virtualCount}个句子级翻译`, { style: translationStyle });

      // 如果启用了悬停功能，添加事件监听器
      if (config.enableHoverUsage) {
        addHoverUsageListeners();
      }

      resolve(appliedCount + virtualCount);
    });
  });
}

// 添加悬停显示用法的事件监听器
function addHoverUsageListeners() {
  const translatedWords = document.querySelectorAll('.translated-word');

  translatedWords.forEach(word => {
    word.addEventListener('mouseenter', showWordUsage);
    word.addEventListener('mouseleave', hideWordUsage);
  });
}

// 显示单词用法
function showWordUsage(event) {
  const element = event.target;
  const original = element.getAttribute('data-original');
  const translated = element.getAttribute('data-translated');

  if (!original || !translated) return;

  // 创建用法提示框
  const usageTooltip = document.createElement('div');
  usageTooltip.id = 'word-usage-tooltip';
  usageTooltip.style.cssText = `
    position: absolute;
    background: #333;
    color: white;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
    max-width: 300px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
  `;

  // 生成用法内容
  const usageContent = generateWordUsage(original, translated);
  usageTooltip.innerHTML = usageContent;

  // 定位提示框
  document.body.appendChild(usageTooltip);

  const rect = element.getBoundingClientRect();
  const tooltipRect = usageTooltip.getBoundingClientRect();

  let left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
  let top = rect.top - tooltipRect.height - 10;

  // 确保提示框在视窗内
  if (left < 10) left = 10;
  if (left + tooltipRect.width > window.innerWidth - 10) {
    left = window.innerWidth - tooltipRect.width - 10;
  }
  if (top < 10) {
    top = rect.bottom + 10;
  }

  usageTooltip.style.left = left + 'px';
  usageTooltip.style.top = top + 'px';

  // 显示动画
  setTimeout(() => {
    usageTooltip.style.opacity = '1';
  }, 10);
}

// 隐藏单词用法
function hideWordUsage() {
  const tooltip = document.getElementById('word-usage-tooltip');
  if (tooltip) {
    tooltip.style.opacity = '0';
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  }
}

// 生成单词用法内容
function generateWordUsage(original, translated) {
  // 这里可以集成真实的词典API，现在先提供模拟内容
  const usageExamples = getWordUsageExamples(original);

  return `
    <div style="margin-bottom: 8px;">
      <strong>${original}</strong> → <strong style="color: #4CAF50;">${translated}</strong>
    </div>
    <div style="font-size: 12px; color: #ccc; margin-bottom: 6px;">用法示例:</div>
    <div style="font-size: 12px; line-height: 1.4;">
      ${usageExamples}
    </div>
  `;
}

// 获取单词用法示例（模拟数据，可以后续集成真实词典API）
function getWordUsageExamples(word) {
  const examples = {
    'hello': '• Hello, how are you? (你好，你好吗？)<br>• Say hello to your family. (向你的家人问好)',
    'world': '• The world is beautiful. (世界很美丽)<br>• Travel around the world. (环游世界)',
    'computer': '• I use a computer for work. (我用电脑工作)<br>• Computer technology is advancing. (计算机技术在进步)',
    'translate': '• Please translate this text. (请翻译这段文字)<br>• Google Translate is useful. (谷歌翻译很有用)',
    'language': '• English is a global language. (英语是全球语言)<br>• Learn a new language. (学习一门新语言)',
    'system': '• The system is working properly. (系统运行正常)<br>• Operating system updates. (操作系统更新)',
    'function': '• This function is useful. (这个功能很有用)<br>• Mathematical functions. (数学函数)',
    'example': '• For example, this works. (例如，这样有效)<br>• Set a good example. (树立好榜样)',
    'content': '• The content is interesting. (内容很有趣)<br>• Website content management. (网站内容管理)',
    'feature': '• New feature released. (新功能发布)<br>• Key features include... (主要功能包括...)'
  };

  const lowerWord = word.toLowerCase();

  if (examples[lowerWord]) {
    return examples[lowerWord];
  }

  // 默认示例
  return `• This is an example of "${word}". (这是"${word}"的一个例子)<br>• "${word}" can be used in different contexts. ("${word}"可以在不同语境中使用)`;
}

// 显示翻译加载中 - 可收缩的悬浮框
function showTranslationLoading() {
  debugLog('显示翻译加载中');

  // 移除现有覆盖层
  const existingOverlay = document.getElementById('translation-overlay');
  if (existingOverlay) {
    existingOverlay.remove();
  }

  // 创建可收缩的悬浮框
  const overlay = document.createElement('div');
  overlay.id = 'translation-overlay';
  overlay.className = 'translation-float-panel';
  overlay.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
    color: white;
    border-radius: 12px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    overflow: hidden;
    max-width: 320px;
    min-width: 280px;
  `;
  
  // 添加CSS样式
  if (!document.getElementById('translation-float-panel-style')) {
    const style = document.createElement('style');
    style.id = 'translation-float-panel-style';
    style.textContent = `
      @keyframes translator-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      .translation-float-panel.collapsed .panel-content {
        max-height: 0;
        padding: 0 20px;
        opacity: 0;
      }
      .translation-float-panel .panel-content {
        max-height: 200px;
        padding: 0 20px 16px 20px;
        opacity: 1;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .translation-float-panel .collapse-icon {
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
      .translation-float-panel.collapsed .collapse-icon {
        transform: rotate(180deg);
      }
      .translation-float-panel.minimized {
        width: 60px;
        min-width: 60px;
      }
      .translation-float-panel.minimized .panel-header-content {
        display: none;
      }
      .translation-float-panel.minimized .panel-content {
        display: none;
      }
    `;
    document.head.appendChild(style);
  }

  // 创建头部区域
  const header = document.createElement('div');
  header.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgba(255,255,255,0.1);
    cursor: pointer;
    user-select: none;
  `;

  // 左侧内容区域
  const leftContent = document.createElement('div');
  leftContent.className = 'panel-header-content';
  leftContent.style.cssText = `
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  `;

  // 添加加载指示器
  const loadingIndicator = document.createElement('div');
  loadingIndicator.style.cssText = `
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255,255,255,0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: translator-spin 1s linear infinite;
    flex-shrink: 0;
  `;

  // 标题文本
  const titleText = document.createElement('div');
  titleText.style.cssText = `
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
  `;
  titleText.textContent = '🌐 翻译中...';

  leftContent.appendChild(loadingIndicator);
  leftContent.appendChild(titleText);

  // 右侧控制按钮区域
  const controls = document.createElement('div');
  controls.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
  `;

  // 收缩/展开按钮
  const collapseBtn = document.createElement('button');
  collapseBtn.className = 'collapse-icon';
  collapseBtn.innerHTML = '▼';
  collapseBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  // 关闭按钮
  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '✕';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  // 按钮悬停效果
  [collapseBtn, closeBtn].forEach(btn => {
    btn.onmouseover = () => btn.style.backgroundColor = 'rgba(255,255,255,0.2)';
    btn.onmouseout = () => btn.style.backgroundColor = 'transparent';
  });

  controls.appendChild(collapseBtn);
  controls.appendChild(closeBtn);

  header.appendChild(leftContent);
  header.appendChild(controls);

  // 内容区域
  const content = document.createElement('div');
  content.className = 'panel-content';

  // 添加进度信息
  const progressInfo = document.createElement('div');
  progressInfo.id = 'translation-progress';
  progressInfo.style.cssText = `
    font-size: 12px;
    line-height: 1.4;
    opacity: 0.9;
    margin-bottom: 8px;
  `;
  progressInfo.textContent = '正在准备翻译...';

  // 添加详细信息
  const detailInfo = document.createElement('div');
  detailInfo.id = 'translation-details';
  detailInfo.style.cssText = `
    font-size: 11px;
    opacity: 0.7;
    line-height: 1.3;
  `;
  detailInfo.textContent = '初始化翻译引擎...';

  content.appendChild(progressInfo);
  content.appendChild(detailInfo);

  overlay.appendChild(header);
  overlay.appendChild(content);

  // 添加事件监听器
  let isCollapsed = false;

  // 收缩/展开功能
  const toggleCollapse = () => {
    isCollapsed = !isCollapsed;
    overlay.classList.toggle('collapsed', isCollapsed);
    collapseBtn.innerHTML = isCollapsed ? '▲' : '▼';
  };

  collapseBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleCollapse();
  });

  header.addEventListener('click', toggleCollapse);

  // 关闭功能
  closeBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    hideTranslationLoading();
  });

  // 添加到页面
  document.body.appendChild(overlay);

  // 入场动画
  overlay.style.transform = 'translateX(100%)';
  overlay.style.opacity = '0';

  requestAnimationFrame(() => {
    overlay.style.transform = 'translateX(0)';
    overlay.style.opacity = '1';
  });

  debugLog('开始翻译过程');
}

// 隐藏翻译加载框
function hideTranslationLoading() {
  const overlay = document.getElementById('translation-overlay');
  if (overlay) {
    overlay.style.transform = 'translateX(100%)';
    overlay.style.opacity = '0';
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 300);
  }
}

// 恢复原始内容
function restoreOriginal() {
  debugLog('恢复原始内容');
  
  if (originalTextMap.size > 0) {
    // 恢复所有被修改的文本节点
    originalTextMap.forEach((originalText, node) => {
      try {
        // 检查节点是否仍在DOM中
        if (!document.contains(node)) {
          return;
        }
        
        // 直接恢复原始文本，不再区分节点类型
        node.textContent = originalText;
        
        // 移除翻译标记
        if (node.dataset) {
          delete node.dataset.translated;
          delete node.dataset.highlighted;
        }
      } catch (e) {
        console.error('恢复节点内容时出错', e);
      }
    });
    
    // 清空映射
    originalTextMap.clear();
    
    // 移除状态指示器
    const statusIndicator = document.getElementById('translation-status');
    if (statusIndicator) {
      statusIndicator.remove();
    }
    
    isTranslated = false;
    debugLog('已恢复所有原始内容');
  } else if (originalContent) {
    // 作为备份，如果没有文本映射，则恢复整个body内容
    // 这种情况不应该发生，但作为安全措施保留
    document.body.innerHTML = originalContent;
    isTranslated = false;
    debugLog('使用备份方法恢复原始内容');
  }
}

// 显示翻译状态指示器 - 可收缩的悬浮框
function showTranslationStatus(message) {
  debugLog('显示翻译状态', message);

  // 移除加载提示
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.transform = 'translateX(100%)';
    loadingOverlay.style.opacity = '0';
    setTimeout(() => loadingOverlay.remove(), 300);
  }

  // 移除现有状态指示器
  const existingIndicator = document.getElementById('translation-status');
  if (existingIndicator) {
    existingIndicator.remove();
  }

  // 创建状态悬浮框
  const statusIndicator = document.createElement('div');
  statusIndicator.id = 'translation-status';
  statusIndicator.className = 'translation-float-panel';
  statusIndicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    color: white;
    border-radius: 12px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    overflow: hidden;
    max-width: 320px;
    min-width: 280px;
  `;
  
  // 创建头部区域
  const header = document.createElement('div');
  header.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgba(255,255,255,0.1);
    cursor: pointer;
    user-select: none;
  `;

  // 左侧内容区域
  const leftContent = document.createElement('div');
  leftContent.className = 'panel-header-content';
  leftContent.style.cssText = `
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  `;

  // 成功图标
  const successIcon = document.createElement('div');
  successIcon.innerHTML = '✅';
  successIcon.style.cssText = `
    font-size: 20px;
    flex-shrink: 0;
  `;

  // 标题文本
  const titleText = document.createElement('div');
  titleText.style.cssText = `
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
  `;
  titleText.textContent = message;

  leftContent.appendChild(successIcon);
  leftContent.appendChild(titleText);

  // 右侧控制按钮区域
  const controls = document.createElement('div');
  controls.style.cssText = `
    display: flex;
    align-items: center;
    gap: 8px;
  `;

  // 收缩/展开按钮
  const collapseBtn = document.createElement('button');
  collapseBtn.className = 'collapse-icon';
  collapseBtn.innerHTML = '▼';
  collapseBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 12px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  // 关闭按钮
  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '✕';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  // 按钮悬停效果
  [collapseBtn, closeBtn].forEach(btn => {
    btn.onmouseover = () => btn.style.backgroundColor = 'rgba(255,255,255,0.2)';
    btn.onmouseout = () => btn.style.backgroundColor = 'transparent';
  });

  controls.appendChild(collapseBtn);
  controls.appendChild(closeBtn);

  header.appendChild(leftContent);
  header.appendChild(controls);

  // 内容区域
  const content = document.createElement('div');
  content.className = 'panel-content';

  // 添加统计信息
  const statsText = document.createElement('div');
  statsText.id = 'translation-stats';
  statsText.style.cssText = `
    font-size: 12px;
    line-height: 1.4;
    opacity: 0.9;
    margin-bottom: 12px;
  `;
  statsText.textContent = '正在收集翻译统计...';

  // 按钮容器
  const buttonContainer = document.createElement('div');
  buttonContainer.style.cssText = `
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  `;

  // 添加恢复按钮
  const restoreButton = document.createElement('button');
  restoreButton.textContent = '🔄 恢复原文';
  restoreButton.style.cssText = `
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;
    flex: 1;
    min-width: 80px;
  `;

  restoreButton.onmouseover = () => {
    restoreButton.style.background = 'rgba(255,255,255,0.3)';
  };
  restoreButton.onmouseout = () => {
    restoreButton.style.background = 'rgba(255,255,255,0.2)';
  };

  restoreButton.onclick = () => {
    debugLog('点击恢复原文按钮');
    restoreOriginal();
  };

  // 添加高亮按钮
  const highlightButton = document.createElement('button');
  highlightButton.textContent = '🔍 高亮翻译';
  highlightButton.style.cssText = `
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;
    flex: 1;
    min-width: 80px;
  `;

  highlightButton.onmouseover = () => {
    highlightButton.style.background = 'rgba(255,255,255,0.3)';
  };
  highlightButton.onmouseout = () => {
    highlightButton.style.background = 'rgba(255,255,255,0.2)';
  };

  highlightButton.onclick = () => {
    debugLog('突出显示翻译内容');
    highlightTranslatedContent();
  };

  buttonContainer.appendChild(restoreButton);
  buttonContainer.appendChild(highlightButton);

  content.appendChild(statsText);
  content.appendChild(buttonContainer);

  statusIndicator.appendChild(header);
  statusIndicator.appendChild(content);

  // 添加事件监听器
  let isCollapsed = false;

  // 收缩/展开功能
  const toggleCollapse = () => {
    isCollapsed = !isCollapsed;
    statusIndicator.classList.toggle('collapsed', isCollapsed);
    collapseBtn.innerHTML = isCollapsed ? '▲' : '▼';
  };

  collapseBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleCollapse();
  });

  header.addEventListener('click', toggleCollapse);

  // 关闭功能
  closeBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    statusIndicator.style.transform = 'translateX(100%)';
    statusIndicator.style.opacity = '0';
    setTimeout(() => {
      if (statusIndicator.parentNode) {
        statusIndicator.parentNode.removeChild(statusIndicator);
      }
    }, 300);
  });

  // 添加到页面
  document.body.appendChild(statusIndicator);

  // 入场动画
  statusIndicator.style.transform = 'translateX(100%)';
  statusIndicator.style.opacity = '0';

  requestAnimationFrame(() => {
    statusIndicator.style.transform = 'translateX(0)';
    statusIndicator.style.opacity = '1';
  });

  // 收集并显示翻译统计信息
  setTimeout(updateTranslationStats, 500);
}

// 突出显示已翻译内容
function highlightTranslatedContent() {
  // 寻找所有带有translated标记的元素
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  
  if (translatedElements.length === 0) {
    alert('未找到已翻译的内容，请尝试先进行翻译');
    return;
  }
  
  // 为每个翻译元素添加高亮效果
  translatedElements.forEach(el => {
    // 检查是否已有高亮
    if (el.dataset.highlighted === 'true') {
      // 移除高亮
      el.style.backgroundColor = '';
      el.style.outline = '';
      delete el.dataset.highlighted;
    } else {
      // 添加高亮
      el.style.backgroundColor = 'rgba(255, 235, 59, 0.3)';
      el.style.outline = '2px solid #ffc107';
      el.dataset.highlighted = 'true';
      
      // 确保元素可见（滚动到视图中）
      el.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  });
}

// 更新翻译统计信息
function updateTranslationStats() {
  const statsElement = document.getElementById('translation-stats');
  if (!statsElement) return;
  
  // 计算已翻译的元素数量
  const translatedElements = document.querySelectorAll('[data-translated="true"]');
  const translatedCount = translatedElements.length;
  
  // 查找带有样式的翻译元素数量
  const underlineElements = document.querySelectorAll('span[style*="text-decoration: underline"]').length;
  const tooltipElements = document.querySelectorAll('span[style*="border-bottom"][style*="dotted"]').length;
  const styledElements = underlineElements + tooltipElements;
  
  // 获取配置
  chrome.runtime.sendMessage({ action: 'getConfig' }, (response) => {
    if (!response || !response.success) return;
    
    const config = response.config;
    statsElement.textContent = `已翻译${translatedCount}个元素 • 样式: ${config.translationStyle || 'replace'} • 比例: ${config.translationPercentage || 100}%`;

    debugLog('翻译统计', {
      translatedCount,
      style: config.translationStyle || 'replace',
      percentage: config.translationPercentage || 100,
      styledElements
    });
  });
}

// 显示翻译错误 - 可关闭的悬浮框
function showTranslationError(errorMessage) {
  debugLog('显示翻译错误', errorMessage);

  // 移除加载提示
  const loadingOverlay = document.getElementById('translation-overlay');
  if (loadingOverlay) {
    loadingOverlay.style.transform = 'translateX(100%)';
    loadingOverlay.style.opacity = '0';
    setTimeout(() => loadingOverlay.remove(), 300);
  }

  // 创建错误悬浮框
  const errorIndicator = document.createElement('div');
  errorIndicator.id = 'translation-error';
  errorIndicator.className = 'translation-float-panel';
  errorIndicator.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    border-radius: 12px;
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    box-shadow: 0 8px 32px rgba(244, 67, 54, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    overflow: hidden;
    max-width: 320px;
    min-width: 280px;
  `;

  // 创建头部区域
  const header = document.createElement('div');
  header.style.cssText = `
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: rgba(255,255,255,0.1);
  `;

  // 左侧内容区域
  const leftContent = document.createElement('div');
  leftContent.style.cssText = `
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  `;

  // 错误图标
  const errorIcon = document.createElement('div');
  errorIcon.innerHTML = '❌';
  errorIcon.style.cssText = `
    font-size: 20px;
    flex-shrink: 0;
  `;

  // 标题文本
  const titleText = document.createElement('div');
  titleText.style.cssText = `
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
  `;
  titleText.textContent = '翻译失败';

  leftContent.appendChild(errorIcon);
  leftContent.appendChild(titleText);

  // 关闭按钮
  const closeBtn = document.createElement('button');
  closeBtn.innerHTML = '✕';
  closeBtn.style.cssText = `
    background: none;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  `;

  closeBtn.onmouseover = () => closeBtn.style.backgroundColor = 'rgba(255,255,255,0.2)';
  closeBtn.onmouseout = () => closeBtn.style.backgroundColor = 'transparent';

  header.appendChild(leftContent);
  header.appendChild(closeBtn);

  // 内容区域
  const content = document.createElement('div');
  content.style.cssText = `
    padding: 0 20px 16px 20px;
  `;

  // 错误消息
  const errorText = document.createElement('div');
  errorText.style.cssText = `
    font-size: 12px;
    line-height: 1.4;
    opacity: 0.9;
    word-break: break-word;
  `;
  errorText.textContent = errorMessage;

  content.appendChild(errorText);

  errorIndicator.appendChild(header);
  errorIndicator.appendChild(content);

  // 关闭功能
  const closeError = () => {
    errorIndicator.style.transform = 'translateX(100%)';
    errorIndicator.style.opacity = '0';
    setTimeout(() => {
      if (errorIndicator.parentNode) {
        errorIndicator.parentNode.removeChild(errorIndicator);
      }
    }, 300);
  };

  closeBtn.addEventListener('click', closeError);

  // 添加到页面
  document.body.appendChild(errorIndicator);

  // 入场动画
  errorIndicator.style.transform = 'translateX(100%)';
  errorIndicator.style.opacity = '0';

  requestAnimationFrame(() => {
    errorIndicator.style.transform = 'translateX(0)';
    errorIndicator.style.opacity = '1';
  });

  // 5秒后自动消失
  setTimeout(closeError, 5000);
}