// 创建Canvas元素
const canvas16 = document.createElement('canvas');
canvas16.width = 16;
canvas16.height = 16;
const ctx16 = canvas16.getContext('2d');

const canvas48 = document.createElement('canvas');
canvas48.width = 48;
canvas48.height = 48;
const ctx48 = canvas48.getContext('2d');

const canvas128 = document.createElement('canvas');
canvas128.width = 128;
canvas128.height = 128;
const ctx128 = canvas128.getContext('2d');

// 绘制16x16图标
function drawIcon16() {
  // 背景
  ctx16.fillStyle = '#4285f4';
  ctx16.fillRect(0, 0, 16, 16);
  
  // 翻译符号
  ctx16.fillStyle = 'white';
  ctx16.font = 'bold 10px Arial';
  ctx16.fillText('T', 5, 11);
}

// 绘制48x48图标
function drawIcon48() {
  // 背景
  ctx48.fillStyle = '#4285f4';
  ctx48.fillRect(0, 0, 48, 48);
  
  // 翻译符号
  ctx48.fillStyle = 'white';
  ctx48.font = 'bold 30px Arial';
  ctx48.fillText('T', 15, 33);
}

// 绘制128x128图标
function drawIcon128() {
  // 背景
  ctx128.fillStyle = '#4285f4';
  ctx128.fillRect(0, 0, 128, 128);
  
  // 翻译符号
  ctx128.fillStyle = 'white';
  ctx128.font = 'bold 80px Arial';
  ctx128.fillText('T', 40, 88);
}

// 导出图标
drawIcon16();
drawIcon48();
drawIcon128();

// 转换为DataURL
const icon16DataURL = canvas16.toDataURL('image/png');
const icon48DataURL = canvas48.toDataURL('image/png');
const icon128DataURL = canvas128.toDataURL('image/png');

// 创建下载链接
const link16 = document.createElement('a');
link16.download = 'icon16.png';
link16.href = icon16DataURL;
link16.click();

const link48 = document.createElement('a');
link48.download = 'icon48.png';
link48.href = icon48DataURL;
link48.click();

const link128 = document.createElement('a');
link128.download = 'icon128.png';
link128.href = icon128DataURL;
link128.click();

console.log('图标已生成。请将生成的图标移动到images文件夹中。'); 